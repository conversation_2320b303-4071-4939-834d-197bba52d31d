package com.ruoyi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class RuoYiApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(RuoYiApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  云垚人工智能启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  ____     __      ____     __        \n" +
                "   \\   \\   /  /   \\   \\   /  /    \n" +
                "   \\  _. /  '          \\  _. /  '       \n" +
                "    _( )_ .'         _( )_ .'         \n" +
                "___(_ o _)'      ___(_ o _)'          \n" +
                "||   |(_,_)'     ||   |(_,_)'         \n" +
                "|   `-'  /       |   `-'  /           \n" +
                " \\      /        \\      /           \n" +
                " `-..-'           `-..-'              ");
    }
}
