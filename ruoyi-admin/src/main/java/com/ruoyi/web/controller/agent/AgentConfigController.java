package com.ruoyi.web.controller.agent;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AgentConfig;
import com.ruoyi.system.service.IAgentConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 智能体配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/agent")
public class AgentConfigController extends BaseController
{
    @Autowired
    private IAgentConfigService agentConfigService;

    /**
     * 查询智能体配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgentConfig agentConfig)
    {
        startPage();
        List<AgentConfig> list = agentConfigService.selectAgentConfigList(agentConfig);
        return getDataTable(list);
    }

    /**
     * 导出智能体配置列表
     */
    @PreAuthorize("@ss.hasPermi('agent:config:export')")
    @Log(title = "智能体配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentConfig agentConfig)
    {
        List<AgentConfig> list = agentConfigService.selectAgentConfigList(agentConfig);
        ExcelUtil<AgentConfig> util = new ExcelUtil<AgentConfig>(AgentConfig.class);
        util.exportExcel(response, list, "智能体配置数据");
    }

    /**
     * 获取智能体配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(agentConfigService.selectAgentConfigById(id));
    }

    /**
     * 新增智能体配置
     */
    @PreAuthorize("@ss.hasPermi('agent:config:add')")
    @Log(title = "智能体配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentConfig agentConfig)
    {
        if (!agentConfigService.checkAgentConfigNameUnique(agentConfig))
        {
            return error("新增智能体'" + agentConfig.getName() + "'失败，智能体名称已存在");
        }
        return toAjax(agentConfigService.insertAgentConfig(agentConfig));
    }

    /**
     * 修改智能体配置
     */
    @PreAuthorize("@ss.hasPermi('agent:config:edit')")
    @Log(title = "智能体配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentConfig agentConfig)
    {
        if (!agentConfigService.checkAgentConfigNameUnique(agentConfig))
        {
            return error("修改智能体'" + agentConfig.getName() + "'失败，智能体名称已存在");
        }
        return toAjax(agentConfigService.updateAgentConfig(agentConfig));
    }

    /**
     * 删除智能体配置
     */
    @PreAuthorize("@ss.hasPermi('agent:config:remove')")
    @Log(title = "智能体配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agentConfigService.deleteAgentConfigByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('agent:config:edit')")
    @Log(title = "智能体配置", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AgentConfig agentConfig)
    {
        return toAjax(agentConfigService.updateAgentConfig(agentConfig));
    }
}
