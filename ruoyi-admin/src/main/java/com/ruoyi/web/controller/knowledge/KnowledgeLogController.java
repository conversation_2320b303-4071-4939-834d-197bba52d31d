package com.ruoyi.web.controller.knowledge;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DocumentCallLog;
import com.ruoyi.system.domain.DocumentDownloadLog;
import com.ruoyi.system.service.IDocumentCallLogService;
import com.ruoyi.system.service.IDocumentDownloadLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 知识库日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/knowledge/logs")
public class KnowledgeLogController extends BaseController
{
    @Autowired
    private IDocumentCallLogService documentCallLogService;

    @Autowired
    private IDocumentDownloadLogService documentDownloadLogService;

    /**
     * 获取文档调用日志
     */
    @PreAuthorize("@ss.hasPermi('knowledge:log:list')")
    @GetMapping("/document-call")
    public TableDataInfo getDocumentCallLogs(DocumentCallLog documentCallLog)
    {
        startPage();
        List<DocumentCallLog> list = documentCallLogService.selectDocumentCallLogList(documentCallLog);
        return getDataTable(list);
    }

    /**
     * 导出文档调用日志
     */
    @PreAuthorize("@ss.hasPermi('knowledge:log:export')")
    @Log(title = "文档调用日志", businessType = BusinessType.EXPORT)
    @PostMapping("/document-call/export")
    public void exportDocumentCallLogs(HttpServletResponse response, DocumentCallLog documentCallLog)
    {
        List<DocumentCallLog> list = documentCallLogService.selectDocumentCallLogList(documentCallLog);
        ExcelUtil<DocumentCallLog> util = new ExcelUtil<DocumentCallLog>(DocumentCallLog.class);
        util.exportExcel(response, list, "文档调用日志数据");
    }

    /**
     * 获取文档下载日志
     */
    @PreAuthorize("@ss.hasPermi('knowledge:log:list')")
    @GetMapping("/document-download")
    public TableDataInfo getDocumentDownloadLogs(DocumentDownloadLog documentDownloadLog)
    {
        startPage();
        List<DocumentDownloadLog> list = documentDownloadLogService.selectDocumentDownloadLogList(documentDownloadLog);
        return getDataTable(list);
    }

    /**
     * 导出文档下载日志
     */
    @PreAuthorize("@ss.hasPermi('knowledge:log:export')")
    @Log(title = "文档下载日志", businessType = BusinessType.EXPORT)
    @PostMapping("/document-download/export")
    public void exportDocumentDownloadLogs(HttpServletResponse response, DocumentDownloadLog documentDownloadLog)
    {
        List<DocumentDownloadLog> list = documentDownloadLogService.selectDocumentDownloadLogList(documentDownloadLog);
        ExcelUtil<DocumentDownloadLog> util = new ExcelUtil<DocumentDownloadLog>(DocumentDownloadLog.class);
        util.exportExcel(response, list, "文档下载日志数据");
    }
}
