package com.ruoyi.web.controller.knowledge;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.KnowledgePermissionTreeNode;
import com.ruoyi.system.service.IKnowledgePermissionService;

/**
 * 知识库权限Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/knowledge/permission")
public class KnowledgePermissionController extends BaseController
{
    @Autowired
    private IKnowledgePermissionService knowledgePermissionService;

    /**
     * 获取机构知识库权限
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:list')")
    @GetMapping("/org")
    public AjaxResult getOrgPermissions(Long orgId)
    {
        List<String> nodeIds = knowledgePermissionService.getOrgPermissionNodeIds(orgId);
        return success(nodeIds);
    }

    /**
     * 设置机构知识库权限
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:edit')")
    @Log(title = "知识库权限", businessType = BusinessType.UPDATE)
    @PostMapping("/org")
    public AjaxResult setOrgPermissions(@RequestBody OrgPermissionRequest request)
    {
        return toAjax(knowledgePermissionService.batchUpdateOrgPermissions(
            request.getOrgId(), request.getOrgName(), request.getNodeIds()));
    }

    /**
     * 获取用户组知识库权限
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:list')")
    @GetMapping("/user-group")
    public AjaxResult getUserGroupPermissions(Long userGroupId)
    {
        List<String> nodeIds = knowledgePermissionService.getUserGroupPermissionNodeIds(userGroupId);
        return success(nodeIds);
    }

    /**
     * 设置用户组知识库权限
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:edit')")
    @Log(title = "知识库权限", businessType = BusinessType.UPDATE)
    @PostMapping("/user-group")
    public AjaxResult setUserGroupPermissions(@RequestBody UserGroupPermissionRequest request)
    {
        return toAjax(knowledgePermissionService.batchUpdateUserGroupPermissions(
            request.getUserGroupId(), request.getUserGroupName(), request.getNodeIds()));
    }

    /**
     * 获取知识库权限树
     */
    @PreAuthorize("@ss.hasPermi('knowledge:permission:list')")
    @GetMapping("/tree")
    public AjaxResult getKnowledgePermissionTree()
    {
        List<KnowledgePermissionTreeNode> tree = knowledgePermissionService.getKnowledgePermissionTree();
        return success(tree);
    }

    /**
     * 机构权限请求对象
     */
    public static class OrgPermissionRequest {
        private Long orgId;
        private String orgName;
        private List<String> nodeIds;

        public Long getOrgId() { return orgId; }
        public void setOrgId(Long orgId) { this.orgId = orgId; }
        public String getOrgName() { return orgName; }
        public void setOrgName(String orgName) { this.orgName = orgName; }
        public List<String> getNodeIds() { return nodeIds; }
        public void setNodeIds(List<String> nodeIds) { this.nodeIds = nodeIds; }
    }

    /**
     * 用户组权限请求对象
     */
    public static class UserGroupPermissionRequest {
        private Long userGroupId;
        private String userGroupName;
        private List<String> nodeIds;

        public Long getUserGroupId() { return userGroupId; }
        public void setUserGroupId(Long userGroupId) { this.userGroupId = userGroupId; }
        public String getUserGroupName() { return userGroupName; }
        public void setUserGroupName(String userGroupName) { this.userGroupName = userGroupName; }
        public List<String> getNodeIds() { return nodeIds; }
        public void setNodeIds(List<String> nodeIds) { this.nodeIds = nodeIds; }
    }
}
