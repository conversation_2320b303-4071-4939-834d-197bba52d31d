package com.ruoyi.web.controller.usergroup;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.UserGroup;
import com.ruoyi.system.service.IUserGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户组Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/user-group")
public class UserGroupController extends BaseController
{
    @Autowired
    private IUserGroupService userGroupService;

    /**
     * 查询用户组列表
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserGroup userGroup)
    {
        startPage();
        List<UserGroup> list = userGroupService.selectUserGroupList(userGroup);
        return getDataTable(list);
    }

    /**
     * 导出用户组列表
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:export')")
    @Log(title = "用户组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserGroup userGroup)
    {
        List<UserGroup> list = userGroupService.selectUserGroupList(userGroup);
        ExcelUtil<UserGroup> util = new ExcelUtil<UserGroup>(UserGroup.class);
        util.exportExcel(response, list, "用户组数据");
    }

    /**
     * 获取用户组详细信息
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userGroupService.selectUserGroupById(id));
    }

    /**
     * 获取用户组成员列表
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:query')")
    @GetMapping(value = "/{id}/members")
    public AjaxResult getMembers(@PathVariable("id") Long id)
    {
        UserGroup userGroup = userGroupService.selectUserGroupById(id);
        if (userGroup != null && userGroup.getMembers() != null)
        {
            return success(userGroup.getMembers());
        }
        return success(new ArrayList<>());
    }

    /**
     * 新增用户组
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:add')")
    @Log(title = "用户组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserGroup userGroup)
    {
        if (!userGroupService.checkUserGroupNameUnique(userGroup))
        {
            return error("新增用户组'" + userGroup.getName() + "'失败，用户组名称已存在");
        }
        return toAjax(userGroupService.insertUserGroup(userGroup));
    }

    /**
     * 修改用户组
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:edit')")
    @Log(title = "用户组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserGroup userGroup)
    {
        if (!userGroupService.checkUserGroupNameUnique(userGroup))
        {
            return error("修改用户组'" + userGroup.getName() + "'失败，用户组名称已存在");
        }
        return toAjax(userGroupService.updateUserGroup(userGroup));
    }

    /**
     * 删除用户组
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:remove')")
    @Log(title = "用户组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userGroupService.deleteUserGroupByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('usergroup:group:edit')")
    @Log(title = "用户组", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody UserGroup userGroup)
    {
        return toAjax(userGroupService.updateUserGroup(userGroup));
    }
}
