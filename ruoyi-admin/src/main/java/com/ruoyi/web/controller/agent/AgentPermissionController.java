package com.ruoyi.web.controller.agent;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AgentPermission;
import com.ruoyi.system.service.IAgentPermissionService;

/**
 * 智能体权限Controller
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/agent/permission")
public class AgentPermissionController extends BaseController
{
    @Autowired
    private IAgentPermissionService agentPermissionService;

    /**
     * 获取机构智能体权限
     */
    @PreAuthorize("@ss.hasPermi('agent:permission:list')")
    @GetMapping("/org")
    public AjaxResult getOrgPermissions(Long orgId)
    {
        AgentPermission agentPermission = new AgentPermission();
        agentPermission.setPermissionType("org");
        agentPermission.setTargetId(orgId);
        List<AgentPermission> list = agentPermissionService.selectAgentPermissionList(agentPermission);
        
        // 提取智能体ID列表，便于前端处理
        List<Long> agentIds = list.stream()
            .map(AgentPermission::getAgentId)
            .collect(java.util.stream.Collectors.toList());
            
        return success(agentIds);
    }

    /**
     * 设置机构智能体权限
     */
    @PreAuthorize("@ss.hasPermi('agent:permission:edit')")
    @Log(title = "智能体权限", businessType = BusinessType.UPDATE)
    @PostMapping("/org")
    public AjaxResult setOrgPermissions(@RequestBody List<AgentPermission> agentPermissions)
    {
        return toAjax(agentPermissionService.batchUpdateAgentPermission(agentPermissions, "org"));
    }

    /**
     * 获取用户组智能体权限
     */
    @PreAuthorize("@ss.hasPermi('agent:permission:list')")
    @GetMapping("/user-group")
    public AjaxResult getUserGroupPermissions(Long userGroupId)
    {
        AgentPermission agentPermission = new AgentPermission();
        agentPermission.setPermissionType("user_group");
        agentPermission.setTargetId(userGroupId);
        List<AgentPermission> list = agentPermissionService.selectAgentPermissionList(agentPermission);
        
        // 提取智能体ID列表，便于前端处理
        List<Long> agentIds = list.stream()
            .map(AgentPermission::getAgentId)
            .collect(java.util.stream.Collectors.toList());
            
        return success(agentIds);
    }

    /**
     * 设置用户组智能体权限
     */
    @PreAuthorize("@ss.hasPermi('agent:permission:edit')")
    @Log(title = "智能体权限", businessType = BusinessType.UPDATE)
    @PostMapping("/user-group")
    public AjaxResult setUserGroupPermissions(@RequestBody List<AgentPermission> agentPermissions)
    {
        return toAjax(agentPermissionService.batchUpdateAgentPermission(agentPermissions, "user_group"));
    }
}
