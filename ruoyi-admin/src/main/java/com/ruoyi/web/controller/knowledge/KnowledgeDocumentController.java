package com.ruoyi.web.controller.knowledge;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.KnowledgeDocument;
import com.ruoyi.system.service.IKnowledgeDocumentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.StringUtils;
import java.security.MessageDigest;

/**
 * 知识库文档Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/knowledge")
public class KnowledgeDocumentController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeDocumentController.class);
    
    // Python服务签名密钥，实际使用时应该放在配置文件中
    private static final String PYTHON_SERVICE_SECRET = "ruoyi-ai-service-2025";

    @Autowired
    private IKnowledgeDocumentService knowledgeDocumentService;

    /**
     * 查询知识库文档列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:list')")
    @GetMapping("/{knowledgeBaseId}/documents")
    public TableDataInfo list(@PathVariable Long knowledgeBaseId, KnowledgeDocument knowledgeDocument)
    {
        startPage();
        knowledgeDocument.setKnowledgeBaseId(knowledgeBaseId);
        List<KnowledgeDocument> list = knowledgeDocumentService.selectKnowledgeDocumentList(knowledgeDocument);
        return getDataTable(list);
    }

    /**
     * 导出知识库文档列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:export')")
    @Log(title = "知识库文档", businessType = BusinessType.EXPORT)
    @PostMapping("/{knowledgeBaseId}/documents/export")
    public void export(HttpServletResponse response, @PathVariable Long knowledgeBaseId, KnowledgeDocument knowledgeDocument)
    {
        knowledgeDocument.setKnowledgeBaseId(knowledgeBaseId);
        List<KnowledgeDocument> list = knowledgeDocumentService.selectKnowledgeDocumentList(knowledgeDocument);
        ExcelUtil<KnowledgeDocument> util = new ExcelUtil<KnowledgeDocument>(KnowledgeDocument.class);
        util.exportExcel(response, list, "知识库文档数据");
    }

    /**
     * 获取知识库文档详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:query')")
    @GetMapping("/{knowledgeBaseId}/documents/{documentId}")
    public AjaxResult getInfo(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @PathVariable("documentId") Long documentId)
    {
        return success(knowledgeDocumentService.selectKnowledgeDocumentById(documentId));
    }

    /**
     * 上传文档到知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:add')")
    @Log(title = "知识库文档", businessType = BusinessType.INSERT)
    @PostMapping("/{knowledgeBaseId}/documents/upload")
    public AjaxResult upload(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, 
                           @RequestParam("files") MultipartFile[] files)
    {
        if (files == null || files.length == 0) {
            return error("请选择要上传的文件");
        }
        
        int successCount = knowledgeDocumentService.uploadDocuments(knowledgeBaseId, files);
        
        if (successCount > 0) {
            return success("成功上传 " + successCount + " 个文件");
        } else {
            return error("文件上传失败");
        }
    }

    /**
     * 修改知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:edit')")
    @Log(title = "知识库文档", businessType = BusinessType.UPDATE)
    @PutMapping("/{knowledgeBaseId}/documents")
    public AjaxResult edit(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @RequestBody KnowledgeDocument knowledgeDocument)
    {
        knowledgeDocument.setKnowledgeBaseId(knowledgeBaseId);
        return toAjax(knowledgeDocumentService.updateKnowledgeDocument(knowledgeDocument));
    }

    /**
     * 解析文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:parse')")
    @Log(title = "知识库文档", businessType = BusinessType.UPDATE)
    @PutMapping("/{knowledgeBaseId}/documents/{documentId}/parse")
    public AjaxResult parse(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @PathVariable("documentId") Long documentId)
    {
        return toAjax(knowledgeDocumentService.parseDocument(documentId));
    }

    /**
     * 批量解析文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:parse')")
    @Log(title = "知识库文档", businessType = BusinessType.UPDATE)
    @PostMapping("/{knowledgeBaseId}/documents/batch-parse")
    public AjaxResult batchParse(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @RequestBody Long[] documentIds)
    {
        return toAjax(knowledgeDocumentService.batchParseDocuments(documentIds));
    }

    /**
     * 删除知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:remove')")
    @Log(title = "知识库文档", businessType = BusinessType.DELETE)
    @DeleteMapping("/{knowledgeBaseId}/documents/{documentIds}")
    public AjaxResult remove(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @PathVariable Long[] documentIds)
    {
        return toAjax(knowledgeDocumentService.deleteKnowledgeDocumentByIds(documentIds));
    }

    /**
     * 批量删除知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:document:remove')")
    @Log(title = "知识库文档", businessType = BusinessType.DELETE)
    @DeleteMapping("/{knowledgeBaseId}/documents/batch-delete")
    public AjaxResult batchRemove(@PathVariable("knowledgeBaseId") Long knowledgeBaseId, @RequestBody Long[] documentIds)
    {
        return toAjax(knowledgeDocumentService.deleteKnowledgeDocumentByIds(documentIds));
    }
    
    /**
     * Python端回调接口 - 更新文档解析状态
     */
    @PostMapping("/callback/parse-status")
    public AjaxResult updateParseStatus(@RequestBody java.util.Map<String, Object> callbackData)
    {
        try {
            // 验证签名
            String sign = (String) callbackData.get("sign");
            if (StringUtils.isEmpty(sign)) {
                return error("签名不能为空");
            }
            
            // 生成预期签名
            String expectedSign = generateSign(callbackData);
            if (!sign.equals(expectedSign)) {
                logger.warn("回调接口签名验证失败，预期: {}, 实际: {}", expectedSign, sign);
                return error("签名验证失败");
            }
            
            Long documentId = Long.valueOf(callbackData.get("documentId").toString());
            String status = callbackData.get("status").toString(); // 2-已解析, 3-解析失败
            Integer progress = Integer.valueOf(callbackData.get("progress").toString());
            String result = callbackData.get("result").toString();
            
            KnowledgeDocument document = new KnowledgeDocument();
            document.setId(documentId);
            document.setStatus(status);
            document.setParseProgress(progress);
            document.setParseResult(result);
            
            return toAjax(knowledgeDocumentService.updateParseStatus(document));
            
        } catch (Exception e) {
            logger.error("更新文档解析状态失败", e);
            return error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成签名
     */
    private String generateSign(java.util.Map<String, Object> data) {
        try {
            // 排除sign字段，按参数名排序生成签名字符串
            StringBuilder signStr = new StringBuilder();
            data.entrySet().stream()
                .filter(entry -> !"sign".equals(entry.getKey()))
                .sorted(java.util.Map.Entry.comparingByKey())
                .forEach(entry -> signStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&"));
            
            // 添加密钥
            signStr.append("secret=").append(PYTHON_SERVICE_SECRET);
            
            // MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes("UTF-8"));
            
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexStr.append('0');
                }
                hexStr.append(hex);
            }
            
            return hexStr.toString().toUpperCase();
            
        } catch (Exception e) {
            logger.error("生成签名失败", e);
            return "";
        }
    }
}
