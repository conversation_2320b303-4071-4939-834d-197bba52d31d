<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KnowledgePermissionMapper">
    
    <resultMap type="KnowledgePermission" id="KnowledgePermissionResult">
        <result property="id"    column="id"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="documentId"    column="document_id"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="targetId"    column="target_id"    />
        <result property="targetName"    column="target_name"    />
        <result property="permissionLevel"    column="permission_level"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgePermissionVo">
        select id, knowledge_base_id, document_id, permission_type, target_id, target_name, permission_level, status, create_time, update_time from knowledge_permission
    </sql>

    <select id="selectKnowledgePermissionList" parameterType="KnowledgePermission" resultMap="KnowledgePermissionResult">
        <include refid="selectKnowledgePermissionVo"/>
        <where>  
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="documentId != null "> and document_id = #{documentId}</if>
            <if test="permissionType != null  and permissionType != ''"> and permission_type = #{permissionType}</if>
            <if test="targetId != null "> and target_id = #{targetId}</if>
            <if test="targetName != null  and targetName != ''"> and target_name like concat('%', #{targetName}, '%')</if>
            <if test="permissionLevel != null  and permissionLevel != ''"> and permission_level = #{permissionLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectKnowledgePermissionById" parameterType="Long" resultMap="KnowledgePermissionResult">
        <include refid="selectKnowledgePermissionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgePermission" parameterType="KnowledgePermission" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="documentId != null">document_id,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="permissionLevel != null">permission_level,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="permissionLevel != null">#{permissionLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgePermission" parameterType="KnowledgePermission">
        update knowledge_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="permissionType != null and permissionType != ''">permission_type = #{permissionType},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="permissionLevel != null">permission_level = #{permissionLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgePermissionById" parameterType="Long">
        delete from knowledge_permission where id = #{id}
    </delete>

    <delete id="deleteKnowledgePermissionByIds" parameterType="String">
        delete from knowledge_permission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectKnowledgePermissionByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgePermissionResult">
        <include refid="selectKnowledgePermissionVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
    </select>
    
    <delete id="deleteKnowledgePermissionByKnowledgeBaseId" parameterType="Long">
        delete from knowledge_permission where knowledge_base_id = #{knowledgeBaseId}
    </delete>
    
    <insert id="batchInsertKnowledgePermission">
        insert into knowledge_permission(knowledge_base_id, document_id, permission_type, target_id, target_name, permission_level, status, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.knowledgeBaseId}, #{item.documentId}, #{item.permissionType}, #{item.targetId}, #{item.targetName}, #{item.permissionLevel}, #{item.status}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
