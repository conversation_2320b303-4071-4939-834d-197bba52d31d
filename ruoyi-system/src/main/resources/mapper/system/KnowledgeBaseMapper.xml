<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KnowledgeBaseMapper">
    
    <resultMap type="KnowledgeBase" id="KnowledgeBaseResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="description"    column="description"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="status"    column="status"    />
        <result property="documentCount"    column="document_count"    />
        <result property="lastUpdateTime"    column="last_update_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeBaseVo">
        select id, name, type, description, create_user_id, create_user_name, org_id, org_name, status, document_count, last_update_time, create_time, update_time from knowledge_base
    </sql>

    <select id="selectKnowledgeBaseList" parameterType="KnowledgeBase" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="createUserId != null "> and create_user_id = #{createUserId}</if>
            <if test="createUserName != null  and createUserName != ''"> and create_user_name like concat('%', #{createUserName}, '%')</if>
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeBaseById" parameterType="Long" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeBase" parameterType="KnowledgeBase" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="description != null">description,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createUserName != null">create_user_name,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="status != null">status,</if>
            <if test="documentCount != null">document_count,</if>
            <if test="lastUpdateTime != null">last_update_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createUserName != null">#{createUserName},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="status != null">#{status},</if>
            <if test="documentCount != null">#{documentCount},</if>
            <if test="lastUpdateTime != null">#{lastUpdateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBase" parameterType="KnowledgeBase">
        update knowledge_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createUserName != null">create_user_name = #{createUserName},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="documentCount != null">document_count = #{documentCount},</if>
            <if test="lastUpdateTime != null">last_update_time = #{lastUpdateTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseById" parameterType="Long">
        delete from knowledge_base where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseByIds" parameterType="String">
        delete from knowledge_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkKnowledgeBaseNameUnique" parameterType="String" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where name = #{name} limit 1
    </select>
    
    <select id="selectKnowledgeBaseByUserId" parameterType="Long" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where create_user_id = #{userId}
    </select>
    
    <select id="selectKnowledgeBaseByOrgId" parameterType="Long" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where org_id = #{orgId}
    </select>
    
    <update id="updateKnowledgeBaseDocumentCount">
        update knowledge_base set document_count = #{documentCount} where id = #{knowledgeBaseId}
    </update>
    
    <update id="updateKnowledgeBaseLastUpdateTime">
        update knowledge_base set last_update_time = #{lastUpdateTime} where id = #{knowledgeBaseId}
    </update>

</mapper>
