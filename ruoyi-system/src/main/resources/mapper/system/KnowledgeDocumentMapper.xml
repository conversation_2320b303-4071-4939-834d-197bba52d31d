<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KnowledgeDocumentMapper">
    
    <resultMap type="KnowledgeDocument" id="KnowledgeDocumentResult">
        <result property="id"    column="id"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="filePath"    column="file_path"    />
        <result property="status"    column="status"    />
        <result property="parseProgress"    column="parse_progress"    />
        <result property="parseResult"    column="parse_result"    />
        <result property="uploadUserId"    column="upload_user_id"    />
        <result property="uploadUserName"    column="upload_user_name"    />
        <result property="downloadCount"    column="download_count"    />
        <result property="lastDownloadTime"    column="last_download_time"    />
        <result property="callCount"    column="call_count"    />
        <result property="lastCallTime"    column="last_call_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeDocumentVo">
        select id, knowledge_base_id, file_name, file_type, file_size, file_path, status, parse_progress, parse_result, upload_user_id, upload_user_name, download_count, last_download_time, call_count, last_call_time, create_time, update_time from knowledge_document
    </sql>

    <select id="selectKnowledgeDocumentList" parameterType="KnowledgeDocument" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        <where>  
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="uploadUserId != null "> and upload_user_id = #{uploadUserId}</if>
            <if test="uploadUserName != null  and uploadUserName != ''"> and upload_user_name like concat('%', #{uploadUserName}, '%')</if>
        </where>
    </select>
    
    <select id="selectKnowledgeDocumentById" parameterType="Long" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeDocument" parameterType="KnowledgeDocument" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="status != null">status,</if>
            <if test="parseProgress != null">parse_progress,</if>
            <if test="parseResult != null">parse_result,</if>
            <if test="uploadUserId != null">upload_user_id,</if>
            <if test="uploadUserName != null">upload_user_name,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="lastDownloadTime != null">last_download_time,</if>
            <if test="callCount != null">call_count,</if>
            <if test="lastCallTime != null">last_call_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="status != null">#{status},</if>
            <if test="parseProgress != null">#{parseProgress},</if>
            <if test="parseResult != null">#{parseResult},</if>
            <if test="uploadUserId != null">#{uploadUserId},</if>
            <if test="uploadUserName != null">#{uploadUserName},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="lastDownloadTime != null">#{lastDownloadTime},</if>
            <if test="callCount != null">#{callCount},</if>
            <if test="lastCallTime != null">#{lastCallTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeDocument" parameterType="KnowledgeDocument">
        update knowledge_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="parseProgress != null">parse_progress = #{parseProgress},</if>
            <if test="parseResult != null">parse_result = #{parseResult},</if>
            <if test="uploadUserId != null">upload_user_id = #{uploadUserId},</if>
            <if test="uploadUserName != null">upload_user_name = #{uploadUserName},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="lastDownloadTime != null">last_download_time = #{lastDownloadTime},</if>
            <if test="callCount != null">call_count = #{callCount},</if>
            <if test="lastCallTime != null">last_call_time = #{lastCallTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeDocumentById" parameterType="Long">
        delete from knowledge_document where id = #{id}
    </delete>

    <delete id="deleteKnowledgeDocumentByIds" parameterType="String">
        delete from knowledge_document where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectKnowledgeDocumentByKnowledgeBaseId" parameterType="Long" resultMap="KnowledgeDocumentResult">
        <include refid="selectKnowledgeDocumentVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
    </select>
    
    <update id="updateKnowledgeDocumentParseStatus">
        update knowledge_document 
        set status = #{status}, parse_progress = #{parseProgress}, parse_result = #{parseResult}
        where id = #{id}
    </update>
    
    <update id="updateKnowledgeDocumentDownloadCount">
        update knowledge_document 
        set download_count = download_count + 1, last_download_time = #{lastDownloadTime}
        where id = #{id}
    </update>
    
    <update id="updateKnowledgeDocumentCallCount">
        update knowledge_document 
        set call_count = call_count + 1, last_call_time = #{lastCallTime}
        where id = #{id}
    </update>
    
    <delete id="deleteKnowledgeDocumentByKnowledgeBaseId" parameterType="Long">
        delete from knowledge_document where knowledge_base_id = #{knowledgeBaseId}
    </delete>

</mapper>
