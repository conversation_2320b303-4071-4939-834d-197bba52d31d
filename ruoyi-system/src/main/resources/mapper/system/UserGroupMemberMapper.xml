<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserGroupMemberMapper">
    
    <resultMap type="UserGroupMember" id="UserGroupMemberResult">
        <result property="id"    column="id"    />
        <result property="groupId"    column="group_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="role"    column="role"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserGroupMemberVo">
        select id, group_id, user_id, user_name, nick_name, dept_name, role, status, create_time, update_time from user_group_member
    </sql>

    <select id="selectUserGroupMemberList" parameterType="UserGroupMember" resultMap="UserGroupMemberResult">
        <include refid="selectUserGroupMemberVo"/>
        <where>  
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="role != null  and role != ''"> and role = #{role}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectUserGroupMemberById" parameterType="Long" resultMap="UserGroupMemberResult">
        <include refid="selectUserGroupMemberVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserGroupMember" parameterType="UserGroupMember" useGeneratedKeys="true" keyProperty="id">
        insert into user_group_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="role != null">role,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="role != null">#{role},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserGroupMember" parameterType="UserGroupMember">
        update user_group_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="role != null">role = #{role},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserGroupMemberById" parameterType="Long">
        delete from user_group_member where id = #{id}
    </delete>

    <delete id="deleteUserGroupMemberByIds" parameterType="String">
        delete from user_group_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectUserGroupMemberByGroupId" parameterType="Long" resultMap="UserGroupMemberResult">
        <include refid="selectUserGroupMemberVo"/>
        where group_id = #{groupId}
    </select>
    
    <delete id="deleteUserGroupMemberByGroupId" parameterType="Long">
        delete from user_group_member where group_id = #{groupId}
    </delete>
    
    <insert id="batchInsertUserGroupMember">
        insert into user_group_member(group_id, user_id, user_name, nick_name, dept_name, role, status, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.groupId}, #{item.userId}, #{item.userName}, #{item.nickName}, #{item.deptName}, #{item.role}, #{item.status}, #{item.createTime})
        </foreach>
    </insert>
    
    <select id="selectUserGroupMemberByGroupIdAndUserId" resultMap="UserGroupMemberResult">
        <include refid="selectUserGroupMemberVo"/>
        where group_id = #{groupId} and user_id = #{userId}
    </select>
    
    <delete id="deleteUserGroupMemberByGroupIdAndUserId">
        delete from user_group_member where group_id = #{groupId} and user_id = #{userId}
    </delete>

</mapper>
