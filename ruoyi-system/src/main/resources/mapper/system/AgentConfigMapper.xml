<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AgentConfigMapper">
    
    <resultMap type="AgentConfig" id="AgentConfigResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="apiKey"    column="api_key"    />
        <result property="model"    column="model"    />
        <result property="temperature"    column="temperature"    />
        <result property="maxTokens"    column="max_tokens"    />
        <result property="description"    column="description"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="status"    column="status"    />
        <result property="systemPrompt"    column="system_prompt"    />
        <result property="contextLength"    column="context_length"    />
        <result property="streamOutput"    column="stream_output"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAgentConfigVo">
        select id, name, api_key, model, temperature, max_tokens, description, create_user_id, create_user_name, status, system_prompt, context_length, stream_output, create_time, update_time from agent_config
    </sql>

    <select id="selectAgentConfigList" parameterType="AgentConfig" resultMap="AgentConfigResult">
        <include refid="selectAgentConfigVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="temperature != null "> and temperature = #{temperature}</if>
            <if test="maxTokens != null "> and max_tokens = #{maxTokens}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="createUserId != null "> and create_user_id = #{createUserId}</if>
            <if test="createUserName != null  and createUserName != ''"> and create_user_name like concat('%', #{createUserName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAgentConfigById" parameterType="Long" resultMap="AgentConfigResult">
        <include refid="selectAgentConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAgentConfig" parameterType="AgentConfig" useGeneratedKeys="true" keyProperty="id">
        insert into agent_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="apiKey != null and apiKey != ''">api_key,</if>
            <if test="model != null and model != ''">model,</if>
            <if test="temperature != null">temperature,</if>
            <if test="maxTokens != null">max_tokens,</if>
            <if test="description != null">description,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createUserName != null">create_user_name,</if>
            <if test="status != null">status,</if>
            <if test="systemPrompt != null">system_prompt,</if>
            <if test="contextLength != null">context_length,</if>
            <if test="streamOutput != null">stream_output,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="apiKey != null and apiKey != ''">#{apiKey},</if>
            <if test="model != null and model != ''">#{model},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="maxTokens != null">#{maxTokens},</if>
            <if test="description != null">#{description},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createUserName != null">#{createUserName},</if>
            <if test="status != null">#{status},</if>
            <if test="systemPrompt != null">#{systemPrompt},</if>
            <if test="contextLength != null">#{contextLength},</if>
            <if test="streamOutput != null">#{streamOutput},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAgentConfig" parameterType="AgentConfig">
        update agent_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="apiKey != null and apiKey != ''">api_key = #{apiKey},</if>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="maxTokens != null">max_tokens = #{maxTokens},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createUserName != null">create_user_name = #{createUserName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="systemPrompt != null">system_prompt = #{systemPrompt},</if>
            <if test="contextLength != null">context_length = #{contextLength},</if>
            <if test="streamOutput != null">stream_output = #{streamOutput},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentConfigById" parameterType="Long">
        delete from agent_config where id = #{id}
    </delete>

    <delete id="deleteAgentConfigByIds" parameterType="String">
        delete from agent_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkAgentConfigNameUnique" parameterType="String" resultMap="AgentConfigResult">
        <include refid="selectAgentConfigVo"/>
        where name = #{name} limit 1
    </select>
    
    <select id="selectAgentConfigByUserId" parameterType="Long" resultMap="AgentConfigResult">
        <include refid="selectAgentConfigVo"/>
        where create_user_id = #{userId}
    </select>

</mapper>
