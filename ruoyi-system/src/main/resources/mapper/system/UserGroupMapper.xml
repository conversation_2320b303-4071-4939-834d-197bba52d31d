<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserGroupMapper">
    
    <resultMap type="UserGroup" id="UserGroupResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgName"    column="org_name"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="status"    column="status"    />
        <result property="memberCount"    column="member_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="UserGroupMember" id="UserGroupMemberResult">
        <result property="id"    column="id"    />
        <result property="groupId"    column="group_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="role"    column="role"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserGroupVo">
        select id, name, description, org_id, org_name, create_user_id, create_user_name, status, member_count, create_time, update_time from user_group
    </sql>

    <select id="selectUserGroupList" parameterType="UserGroup" resultMap="UserGroupResult">
        <include refid="selectUserGroupVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="createUserId != null "> and create_user_id = #{createUserId}</if>
            <if test="createUserName != null  and createUserName != ''"> and create_user_name like concat('%', #{createUserName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectUserGroupById" parameterType="Long" resultMap="UserGroupResult">
        <include refid="selectUserGroupVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserGroup" parameterType="UserGroup" useGeneratedKeys="true" keyProperty="id">
        insert into user_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createUserName != null">create_user_name,</if>
            <if test="status != null">status,</if>
            <if test="memberCount != null">member_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createUserName != null">#{createUserName},</if>
            <if test="status != null">#{status},</if>
            <if test="memberCount != null">#{memberCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserGroup" parameterType="UserGroup">
        update user_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createUserName != null">create_user_name = #{createUserName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="memberCount != null">member_count = #{memberCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserGroupById" parameterType="Long">
        delete from user_group where id = #{id}
    </delete>

    <delete id="deleteUserGroupByIds" parameterType="String">
        delete from user_group where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkUserGroupNameUnique" parameterType="String" resultMap="UserGroupResult">
        <include refid="selectUserGroupVo"/>
        where name = #{name} limit 1
    </select>
    
    <!-- 删除用户组的所有成员 -->
    <delete id="deleteUserGroupMembersByGroupId" parameterType="Long">
        delete from user_group_member where group_id = #{groupId}
    </delete>
    
    <!-- 批量插入用户组成员 -->
    <insert id="insertUserGroupMembers" parameterType="java.util.List">
        insert into user_group_member (group_id, user_id, user_name, nick_name, dept_name, role, status, create_time)
        values
        <foreach collection="list" item="member" separator=",">
            (#{member.groupId}, #{member.userId}, #{member.userName}, #{member.nickName}, #{member.deptName}, #{member.role}, #{member.status}, #{member.createTime})
        </foreach>
    </insert>
    
    <!-- 根据用户组ID查询成员列表 -->
    <select id="selectUserGroupMembersByGroupId" parameterType="Long" resultMap="UserGroupMemberResult">
        select m.id, m.group_id, m.user_id, m.user_name, m.nick_name, m.dept_name, m.role, m.status, m.create_time, m.update_time
        from user_group_member m
        where m.group_id = #{groupId}
        order by m.create_time desc
    </select>

</mapper>
