<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AgentPermissionMapper">
    
    <resultMap type="AgentPermission" id="AgentPermissionResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="targetId"    column="target_id"    />
        <result property="targetName"    column="target_name"    />
        <result property="permissionLevel"    column="permission_level"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAgentPermissionVo">
        select id, agent_id, permission_type, target_id, target_name, permission_level, status, create_time, update_time from agent_permission
    </sql>

    <select id="selectAgentPermissionList" parameterType="AgentPermission" resultMap="AgentPermissionResult">
        <include refid="selectAgentPermissionVo"/>
        <where>  
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="permissionType != null  and permissionType != ''"> and permission_type = #{permissionType}</if>
            <if test="targetId != null "> and target_id = #{targetId}</if>
            <if test="targetName != null  and targetName != ''"> and target_name like concat('%', #{targetName}, '%')</if>
            <if test="permissionLevel != null  and permissionLevel != ''"> and permission_level = #{permissionLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAgentPermissionById" parameterType="Long" resultMap="AgentPermissionResult">
        <include refid="selectAgentPermissionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAgentPermission" parameterType="AgentPermission" useGeneratedKeys="true" keyProperty="id">
        insert into agent_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="permissionLevel != null">permission_level,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="permissionLevel != null">#{permissionLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAgentPermission" parameterType="AgentPermission">
        update agent_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="permissionType != null and permissionType != ''">permission_type = #{permissionType},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="permissionLevel != null">permission_level = #{permissionLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentPermissionById" parameterType="Long">
        delete from agent_permission where id = #{id}
    </delete>

    <delete id="deleteAgentPermissionByIds" parameterType="String">
        delete from agent_permission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectAgentPermissionByAgentId" parameterType="Long" resultMap="AgentPermissionResult">
        <include refid="selectAgentPermissionVo"/>
        where agent_id = #{agentId}
    </select>
    
    <delete id="deleteAgentPermissionByAgentId" parameterType="Long">
        delete from agent_permission where agent_id = #{agentId}
    </delete>
    
    <insert id="batchInsertAgentPermission">
        insert into agent_permission(agent_id, permission_type, target_id, target_name, permission_level, status, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.agentId}, #{item.permissionType}, #{item.targetId}, #{item.targetName}, #{item.permissionLevel}, #{item.status}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
