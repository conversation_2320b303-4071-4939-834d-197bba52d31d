<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DocumentDownloadLogMapper">
    
    <resultMap type="DocumentDownloadLog" id="DocumentDownloadLogResult">
        <result property="id"    column="id"    />
        <result property="documentId"    column="document_id"    />
        <result property="documentName"    column="document_name"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="knowledgeBaseName"    column="knowledge_base_name"    />
        <result property="downloadUserId"    column="download_user_id"    />
        <result property="downloadUserName"    column="download_user_name"    />
        <result property="downloadTime"    column="download_time"    />
        <result property="downloadType"    column="download_type"    />
        <result property="downloadResult"    column="download_result"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="downloadIp"    column="download_ip"    />
        <result property="fileSize"    column="file_size"    />
        <result property="downloadDuration"    column="download_duration"    />
    </resultMap>

    <sql id="selectDocumentDownloadLogVo">
        select id, document_id, document_name, knowledge_base_id, knowledge_base_name, download_user_id, download_user_name, download_time, download_type, download_result, error_message, download_ip, file_size, download_duration from document_download_log
    </sql>

    <select id="selectDocumentDownloadLogList" parameterType="DocumentDownloadLog" resultMap="DocumentDownloadLogResult">
        <include refid="selectDocumentDownloadLogVo"/>
        <where>  
            <if test="documentId != null "> and document_id = #{documentId}</if>
            <if test="documentName != null  and documentName != ''"> and document_name like concat('%', #{documentName}, '%')</if>
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="knowledgeBaseName != null  and knowledgeBaseName != ''"> and knowledge_base_name like concat('%', #{knowledgeBaseName}, '%')</if>
            <if test="downloadUserId != null "> and download_user_id = #{downloadUserId}</if>
            <if test="downloadUserName != null  and downloadUserName != ''"> and download_user_name like concat('%', #{downloadUserName}, '%')</if>
            <if test="downloadType != null  and downloadType != ''"> and download_type = #{downloadType}</if>
            <if test="downloadResult != null  and downloadResult != ''"> and download_result = #{downloadResult}</if>
        </where>
        order by download_time desc
    </select>
    
    <select id="selectDocumentDownloadLogById" parameterType="Long" resultMap="DocumentDownloadLogResult">
        <include refid="selectDocumentDownloadLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDocumentDownloadLog" parameterType="DocumentDownloadLog" useGeneratedKeys="true" keyProperty="id">
        insert into document_download_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="documentId != null">document_id,</if>
            <if test="documentName != null">document_name,</if>
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="knowledgeBaseName != null">knowledge_base_name,</if>
            <if test="downloadUserId != null">download_user_id,</if>
            <if test="downloadUserName != null">download_user_name,</if>
            <if test="downloadTime != null">download_time,</if>
            <if test="downloadType != null">download_type,</if>
            <if test="downloadResult != null">download_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="downloadIp != null">download_ip,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="downloadDuration != null">download_duration,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="documentId != null">#{documentId},</if>
            <if test="documentName != null">#{documentName},</if>
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="knowledgeBaseName != null">#{knowledgeBaseName},</if>
            <if test="downloadUserId != null">#{downloadUserId},</if>
            <if test="downloadUserName != null">#{downloadUserName},</if>
            <if test="downloadTime != null">#{downloadTime},</if>
            <if test="downloadType != null">#{downloadType},</if>
            <if test="downloadResult != null">#{downloadResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="downloadIp != null">#{downloadIp},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="downloadDuration != null">#{downloadDuration},</if>
         </trim>
    </insert>

    <update id="updateDocumentDownloadLog" parameterType="DocumentDownloadLog">
        update document_download_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="documentName != null">document_name = #{documentName},</if>
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="knowledgeBaseName != null">knowledge_base_name = #{knowledgeBaseName},</if>
            <if test="downloadUserId != null">download_user_id = #{downloadUserId},</if>
            <if test="downloadUserName != null">download_user_name = #{downloadUserName},</if>
            <if test="downloadTime != null">download_time = #{downloadTime},</if>
            <if test="downloadType != null">download_type = #{downloadType},</if>
            <if test="downloadResult != null">download_result = #{downloadResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="downloadIp != null">download_ip = #{downloadIp},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="downloadDuration != null">download_duration = #{downloadDuration},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDocumentDownloadLogById" parameterType="Long">
        delete from document_download_log where id = #{id}
    </delete>

    <delete id="deleteDocumentDownloadLogByIds" parameterType="String">
        delete from document_download_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectDocumentDownloadLogByDocumentId" parameterType="Long" resultMap="DocumentDownloadLogResult">
        <include refid="selectDocumentDownloadLogVo"/>
        where document_id = #{documentId}
        order by download_time desc
    </select>
    
    <select id="selectDocumentDownloadLogByKnowledgeBaseId" parameterType="Long" resultMap="DocumentDownloadLogResult">
        <include refid="selectDocumentDownloadLogVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
        order by download_time desc
    </select>

</mapper>
