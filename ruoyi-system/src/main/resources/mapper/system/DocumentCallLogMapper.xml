<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DocumentCallLogMapper">
    
    <resultMap type="DocumentCallLog" id="DocumentCallLogResult">
        <result property="id"    column="id"    />
        <result property="documentId"    column="document_id"    />
        <result property="documentName"    column="document_name"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="knowledgeBaseName"    column="knowledge_base_name"    />
        <result property="callUserId"    column="call_user_id"    />
        <result property="callUserName"    column="call_user_name"    />
        <result property="callTime"    column="call_time"    />
        <result property="callType"    column="call_type"    />
        <result property="callResult"    column="call_result"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="callIp"    column="call_ip"    />
        <result property="responseTime"    column="response_time"    />
    </resultMap>

    <sql id="selectDocumentCallLogVo">
        select id, document_id, document_name, knowledge_base_id, knowledge_base_name, call_user_id, call_user_name, call_time, call_type, call_result, error_message, call_ip, response_time from document_call_log
    </sql>

    <select id="selectDocumentCallLogList" parameterType="DocumentCallLog" resultMap="DocumentCallLogResult">
        <include refid="selectDocumentCallLogVo"/>
        <where>  
            <if test="documentId != null "> and document_id = #{documentId}</if>
            <if test="documentName != null  and documentName != ''"> and document_name like concat('%', #{documentName}, '%')</if>
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="knowledgeBaseName != null  and knowledgeBaseName != ''"> and knowledge_base_name like concat('%', #{knowledgeBaseName}, '%')</if>
            <if test="callUserId != null "> and call_user_id = #{callUserId}</if>
            <if test="callUserName != null  and callUserName != ''"> and call_user_name like concat('%', #{callUserName}, '%')</if>
            <if test="callType != null  and callType != ''"> and call_type = #{callType}</if>
            <if test="callResult != null  and callResult != ''"> and call_result = #{callResult}</if>
        </where>
        order by call_time desc
    </select>
    
    <select id="selectDocumentCallLogById" parameterType="Long" resultMap="DocumentCallLogResult">
        <include refid="selectDocumentCallLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDocumentCallLog" parameterType="DocumentCallLog" useGeneratedKeys="true" keyProperty="id">
        insert into document_call_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="documentId != null">document_id,</if>
            <if test="documentName != null">document_name,</if>
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="knowledgeBaseName != null">knowledge_base_name,</if>
            <if test="callUserId != null">call_user_id,</if>
            <if test="callUserName != null">call_user_name,</if>
            <if test="callTime != null">call_time,</if>
            <if test="callType != null">call_type,</if>
            <if test="callResult != null">call_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="callIp != null">call_ip,</if>
            <if test="responseTime != null">response_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="documentId != null">#{documentId},</if>
            <if test="documentName != null">#{documentName},</if>
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="knowledgeBaseName != null">#{knowledgeBaseName},</if>
            <if test="callUserId != null">#{callUserId},</if>
            <if test="callUserName != null">#{callUserName},</if>
            <if test="callTime != null">#{callTime},</if>
            <if test="callType != null">#{callType},</if>
            <if test="callResult != null">#{callResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="callIp != null">#{callIp},</if>
            <if test="responseTime != null">#{responseTime},</if>
         </trim>
    </insert>

    <update id="updateDocumentCallLog" parameterType="DocumentCallLog">
        update document_call_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="documentName != null">document_name = #{documentName},</if>
            <if test="knowledgeBaseId != null">knowledge_base_id = #{knowledgeBaseId},</if>
            <if test="knowledgeBaseName != null">knowledge_base_name = #{knowledgeBaseName},</if>
            <if test="callUserId != null">call_user_id = #{callUserId},</if>
            <if test="callUserName != null">call_user_name = #{callUserName},</if>
            <if test="callTime != null">call_time = #{callTime},</if>
            <if test="callType != null">call_type = #{callType},</if>
            <if test="callResult != null">call_result = #{callResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="callIp != null">call_ip = #{callIp},</if>
            <if test="responseTime != null">response_time = #{responseTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDocumentCallLogById" parameterType="Long">
        delete from document_call_log where id = #{id}
    </delete>

    <delete id="deleteDocumentCallLogByIds" parameterType="String">
        delete from document_call_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectDocumentCallLogByDocumentId" parameterType="Long" resultMap="DocumentCallLogResult">
        <include refid="selectDocumentCallLogVo"/>
        where document_id = #{documentId}
        order by call_time desc
    </select>
    
    <select id="selectDocumentCallLogByKnowledgeBaseId" parameterType="Long" resultMap="DocumentCallLogResult">
        <include refid="selectDocumentCallLogVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
        order by call_time desc
    </select>

</mapper>
