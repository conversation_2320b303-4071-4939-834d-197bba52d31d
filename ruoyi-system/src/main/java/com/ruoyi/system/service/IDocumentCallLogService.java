package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.DocumentCallLog;

/**
 * 文档调用日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IDocumentCallLogService
{
    /**
     * 查询文档调用日志
     * 
     * @param id 文档调用日志主键
     * @return 文档调用日志
     */
    public DocumentCallLog selectDocumentCallLogById(Long id);

    /**
     * 查询文档调用日志列表
     * 
     * @param documentCallLog 文档调用日志
     * @return 文档调用日志集合
     */
    public List<DocumentCallLog> selectDocumentCallLogList(DocumentCallLog documentCallLog);

    /**
     * 新增文档调用日志
     * 
     * @param documentCallLog 文档调用日志
     * @return 结果
     */
    public int insertDocumentCallLog(DocumentCallLog documentCallLog);

    /**
     * 修改文档调用日志
     * 
     * @param documentCallLog 文档调用日志
     * @return 结果
     */
    public int updateDocumentCallLog(DocumentCallLog documentCallLog);

    /**
     * 批量删除文档调用日志
     * 
     * @param ids 需要删除的文档调用日志主键集合
     * @return 结果
     */
    public int deleteDocumentCallLogByIds(Long[] ids);

    /**
     * 删除文档调用日志信息
     * 
     * @param id 文档调用日志主键
     * @return 结果
     */
    public int deleteDocumentCallLogById(Long id);

    /**
     * 根据文档ID查询调用日志列表
     * 
     * @param documentId 文档ID
     * @return 文档调用日志集合
     */
    public List<DocumentCallLog> selectDocumentCallLogByDocumentId(Long documentId);

    /**
     * 根据知识库ID查询调用日志列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 文档调用日志集合
     */
    public List<DocumentCallLog> selectDocumentCallLogByKnowledgeBaseId(Long knowledgeBaseId);
}
