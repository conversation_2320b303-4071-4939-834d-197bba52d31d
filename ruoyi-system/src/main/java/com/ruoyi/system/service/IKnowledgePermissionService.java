package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.KnowledgePermission;
import com.ruoyi.system.domain.KnowledgePermissionTreeNode;

/**
 * 知识库权限Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IKnowledgePermissionService
{
    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    public KnowledgePermission selectKnowledgePermissionById(Long id);

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgePermission 知识库权限
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionList(KnowledgePermission knowledgePermission);

    /**
     * 新增知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    public int insertKnowledgePermission(KnowledgePermission knowledgePermission);

    /**
     * 修改知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    public int updateKnowledgePermission(KnowledgePermission knowledgePermission);

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的知识库权限主键集合
     * @return 结果
     */
    public int deleteKnowledgePermissionByIds(Long[] ids);

    /**
     * 删除知识库权限信息
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    public int deleteKnowledgePermissionById(Long id);

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionByBaseId(Long knowledgeBaseId);

    /**
     * 根据知识库ID和权限类型查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @param permissionType 权限类型
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionByBaseIdAndType(Long knowledgeBaseId, String permissionType);

    /**
     * 批量更新知识库权限
     * 
     * @param knowledgePermissions 知识库权限集合
     * @param permissionType 权限类型
     * @return 结果
     */
    public int batchUpdateKnowledgePermission(List<KnowledgePermission> knowledgePermissions, String permissionType);

    /**
     * 获取知识库和文档权限树
     * 
     * @return 权限树节点集合
     */
    public List<KnowledgePermissionTreeNode> getKnowledgePermissionTree();

    /**
     * 获取机构的权限节点ID列表
     * 
     * @param orgId 机构ID
     * @return 权限节点ID列表
     */
    public List<String> getOrgPermissionNodeIds(Long orgId);

    /**
     * 获取用户组的权限节点ID列表
     * 
     * @param userGroupId 用户组ID
     * @return 权限节点ID列表
     */
    public List<String> getUserGroupPermissionNodeIds(Long userGroupId);

    /**
     * 批量更新机构权限（支持文档级别）
     * 
     * @param orgId 机构ID
     * @param orgName 机构名称
     * @param nodeIds 权限节点ID列表
     * @return 结果
     */
    public int batchUpdateOrgPermissions(Long orgId, String orgName, List<String> nodeIds);

    /**
     * 批量更新用户组权限（支持文档级别）
     * 
     * @param userGroupId 用户组ID
     * @param userGroupName 用户组名称
     * @param nodeIds 权限节点ID列表
     * @return 结果
     */
    public int batchUpdateUserGroupPermissions(Long userGroupId, String userGroupName, List<String> nodeIds);
}
