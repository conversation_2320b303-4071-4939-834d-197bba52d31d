package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.UserGroup;
import com.ruoyi.system.domain.UserGroupMember;
import com.ruoyi.system.mapper.UserGroupMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.IUserGroupService;

/**
 * 用户组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class UserGroupServiceImpl implements IUserGroupService
{
    @Autowired
    private UserGroupMapper userGroupMapper;
    
    @Autowired
    private ISysUserService userService;

    /**
     * 查询用户组
     * 
     * @param id 用户组主键
     * @return 用户组
     */
    @Override
    public UserGroup selectUserGroupById(Long id)
    {
        UserGroup userGroup = userGroupMapper.selectUserGroupById(id);
        if (userGroup != null)
        {
            // 加载用户组成员
            List<UserGroupMember> members = userGroupMapper.selectUserGroupMembersByGroupId(id);
            userGroup.setMembers(members);
            
            // 设置用户IDs数组
            if (members != null && !members.isEmpty())
            {
                Long[] userIds = members.stream()
                    .map(UserGroupMember::getUserId)
                    .toArray(Long[]::new);
                userGroup.setUserIds(userIds);
            }
            else
            {
                userGroup.setUserIds(new Long[0]);
            }
            
            // 更新成员数量
            userGroup.setMemberCount(members != null ? members.size() : 0);
        }
        return userGroup;
    }

    /**
     * 查询用户组列表
     * 
     * @param userGroup 用户组
     * @return 用户组
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<UserGroup> selectUserGroupList(UserGroup userGroup)
    {
        return userGroupMapper.selectUserGroupList(userGroup);
    }

    /**
     * 新增用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUserGroup(UserGroup userGroup)
    {
        userGroup.setCreateTime(DateUtils.getNowDate());
        userGroup.setCreateUserId(SecurityUtils.getUserId());
        userGroup.setCreateUserName(SecurityUtils.getUsername());
        
        int result = userGroupMapper.insertUserGroup(userGroup);
        // 只有当userIds不为null且不为空时才处理成员关系
        if (result > 0 && userGroup.getUserIds() != null && userGroup.getUserIds().length > 0)
        {
            List<UserGroupMember> members = new ArrayList<>();
            for (Long userId : userGroup.getUserIds())
            {
                UserGroupMember member = createUserGroupMember(userGroup.getId(), userId);
                members.add(member);
            }
            userGroupMapper.insertUserGroupMembers(members);
            
            // 更新成员数量
            userGroup.setMemberCount(members.size());
            userGroupMapper.updateUserGroup(userGroup);
        }
        else
        {
            // 如果没有成员，设置成员数量为0
            userGroup.setMemberCount(0);
            userGroupMapper.updateUserGroup(userGroup);
        }
        
        return result;
    }

    /**
     * 修改用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUserGroup(UserGroup userGroup)
    {
        userGroup.setUpdateTime(DateUtils.getNowDate());
        int result = userGroupMapper.updateUserGroup(userGroup);
        
        // 只有当userIds不为null时才处理成员关系（说明前端传递了成员信息）
        if (result > 0 && userGroup.getUserIds() != null)
        {
            // 先删除现有的成员关系
            userGroupMapper.deleteUserGroupMembersByGroupId(userGroup.getId());
            
            // 插入新的成员关系
            if (userGroup.getUserIds().length > 0)
            {
                List<UserGroupMember> members = new ArrayList<>();
                for (Long userId : userGroup.getUserIds())
                {
                    if (userId != null && userId > 0) // 确保userId有效
                    {
                        UserGroupMember member = createUserGroupMember(userGroup.getId(), userId);
                        members.add(member);
                    }
                }
                
                if (!members.isEmpty())
                {
                    userGroupMapper.insertUserGroupMembers(members);
                }
                
                // 更新成员数量
                userGroup.setMemberCount(members.size());
            }
            else
            {
                // 如果userIds为空数组，成员数量设为0
                userGroup.setMemberCount(0);
            }
            
            // 重新更新用户组的成员数量
            userGroupMapper.updateUserGroup(userGroup);
        }
        
        return result;
    }

    /**
     * 批量删除用户组
     * 
     * @param ids 需要删除的用户组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserGroupByIds(Long[] ids)
    {
        // 先删除用户组成员关系
        for (Long id : ids)
        {
            userGroupMapper.deleteUserGroupMembersByGroupId(id);
        }
        // 再删除用户组
        return userGroupMapper.deleteUserGroupByIds(ids);
    }

    /**
     * 删除用户组信息
     * 
     * @param id 用户组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserGroupById(Long id)
    {
        // 先删除用户组成员关系
        userGroupMapper.deleteUserGroupMembersByGroupId(id);
        // 再删除用户组
        return userGroupMapper.deleteUserGroupById(id);
    }

    /**
     * 创建用户组成员对象
     * 
     * @param groupId 用户组ID
     * @param userId 用户ID
     * @return 用户组成员对象
     */
    private UserGroupMember createUserGroupMember(Long groupId, Long userId)
    {
        UserGroupMember member = new UserGroupMember();
        member.setGroupId(groupId);
        member.setUserId(userId);
        member.setRole("member"); // 默认角色为成员
        member.setStatus("0"); // 默认状态为正常
        member.setCreateTime(DateUtils.getNowDate());
        
        // 查询用户信息并设置
        try {
            SysUser user = userService.selectUserById(userId);
            if (user != null)
            {
                member.setUserName(StringUtils.isNotEmpty(user.getUserName()) ? user.getUserName() : "");
                member.setNickName(StringUtils.isNotEmpty(user.getNickName()) ? user.getNickName() : "");
                if (user.getDept() != null && StringUtils.isNotEmpty(user.getDept().getDeptName()))
                {
                    member.setDeptName(user.getDept().getDeptName());
                }
                else
                {
                    member.setDeptName("");
                }
            }
            else
            {
                // 如果用户不存在，设置默认值
                member.setUserName("");
                member.setNickName("");
                member.setDeptName("");
            }
        } catch (Exception e) {
            // 如果查询用户失败，设置默认值
            member.setUserName("");
            member.setNickName("");
            member.setDeptName("");
        }
        
        return member;
    }

    /**
     * 校验用户组名称是否唯一
     * 
     * @param userGroup 用户组信息
     * @return 结果
     */
    @Override
    public boolean checkUserGroupNameUnique(UserGroup userGroup)
    {
        Long groupId = StringUtils.isNull(userGroup.getId()) ? -1L : userGroup.getId();
        UserGroup info = userGroupMapper.checkUserGroupNameUnique(userGroup.getName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != groupId.longValue())
        {
            return false;
        }
        return true;
    }

}
