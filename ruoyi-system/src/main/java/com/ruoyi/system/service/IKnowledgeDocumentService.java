package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.KnowledgeDocument;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库文档Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IKnowledgeDocumentService
{
    /**
     * 查询知识库文档
     * 
     * @param id 知识库文档主键
     * @return 知识库文档
     */
    public KnowledgeDocument selectKnowledgeDocumentById(Long id);

    /**
     * 查询知识库文档列表
     * 
     * @param knowledgeDocument 知识库文档
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentList(KnowledgeDocument knowledgeDocument);

    /**
     * 新增知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int insertKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 修改知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int updateKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 批量删除知识库文档
     * 
     * @param ids 需要删除的知识库文档主键集合
     * @return 结果
     */
    public int deleteKnowledgeDocumentByIds(Long[] ids);

    /**
     * 删除知识库文档信息
     * 
     * @param id 知识库文档主键
     * @return 结果
     */
    public int deleteKnowledgeDocumentById(Long id);

    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 上传文档到知识库
     * 
     * @param knowledgeBaseId 知识库ID
     * @param files 文件列表
     * @return 结果
     */
    public int uploadDocuments(Long knowledgeBaseId, MultipartFile[] files);

    /**
     * 解析文档
     * 
     * @param documentId 文档ID
     * @return 结果
     */
    public int parseDocument(Long documentId);

    /**
     * 批量解析文档
     * 
     * @param ids 需要解析的文档ID数组
     * @return 结果
     */
    public int batchParseDocuments(Long[] ids);

    /**
     * 更新文档解析状态
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int updateParseStatus(KnowledgeDocument knowledgeDocument);
}
