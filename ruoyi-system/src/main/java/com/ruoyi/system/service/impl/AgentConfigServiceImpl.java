package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.AgentConfig;
import com.ruoyi.system.mapper.AgentConfigMapper;
import com.ruoyi.system.service.IAgentConfigService;

/**
 * 智能体配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class AgentConfigServiceImpl implements IAgentConfigService
{
    @Autowired
    private AgentConfigMapper agentConfigMapper;

    /**
     * 查询智能体配置
     * 
     * @param id 智能体配置主键
     * @return 智能体配置
     */
    @Override
    public AgentConfig selectAgentConfigById(Long id)
    {
        return agentConfigMapper.selectAgentConfigById(id);
    }

    /**
     * 查询智能体配置列表
     * 
     * @param agentConfig 智能体配置
     * @return 智能体配置
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<AgentConfig> selectAgentConfigList(AgentConfig agentConfig)
    {
        return agentConfigMapper.selectAgentConfigList(agentConfig);
    }

    /**
     * 新增智能体配置
     * 
     * @param agentConfig 智能体配置
     * @return 结果
     */
    @Override
    public int insertAgentConfig(AgentConfig agentConfig)
    {
        agentConfig.setCreateTime(DateUtils.getNowDate());
        agentConfig.setCreateUserId(SecurityUtils.getUserId());
        agentConfig.setCreateUserName(SecurityUtils.getUsername());
        return agentConfigMapper.insertAgentConfig(agentConfig);
    }

    /**
     * 修改智能体配置
     * 
     * @param agentConfig 智能体配置
     * @return 结果
     */
    @Override
    public int updateAgentConfig(AgentConfig agentConfig)
    {
        agentConfig.setUpdateTime(DateUtils.getNowDate());
        return agentConfigMapper.updateAgentConfig(agentConfig);
    }

    /**
     * 批量删除智能体配置
     * 
     * @param ids 需要删除的智能体配置主键
     * @return 结果
     */
    @Override
    public int deleteAgentConfigByIds(Long[] ids)
    {
        return agentConfigMapper.deleteAgentConfigByIds(ids);
    }

    /**
     * 删除智能体配置信息
     * 
     * @param id 智能体配置主键
     * @return 结果
     */
    @Override
    public int deleteAgentConfigById(Long id)
    {
        return agentConfigMapper.deleteAgentConfigById(id);
    }

    /**
     * 校验智能体名称是否唯一
     * 
     * @param agentConfig 智能体配置信息
     * @return 结果
     */
    @Override
    public boolean checkAgentConfigNameUnique(AgentConfig agentConfig)
    {
        Long agentId = StringUtils.isNull(agentConfig.getId()) ? -1L : agentConfig.getId();
        AgentConfig info = agentConfigMapper.checkAgentConfigNameUnique(agentConfig);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != agentId.longValue())
        {
            return false;
        }
        return true;
    }
}
