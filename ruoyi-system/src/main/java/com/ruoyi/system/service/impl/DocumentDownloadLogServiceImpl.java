package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.DocumentDownloadLog;
import com.ruoyi.system.mapper.DocumentDownloadLogMapper;
import com.ruoyi.system.service.IDocumentDownloadLogService;

/**
 * 文档下载日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class DocumentDownloadLogServiceImpl implements IDocumentDownloadLogService
{
    @Autowired
    private DocumentDownloadLogMapper documentDownloadLogMapper;

    /**
     * 查询文档下载日志
     * 
     * @param id 文档下载日志主键
     * @return 文档下载日志
     */
    @Override
    public DocumentDownloadLog selectDocumentDownloadLogById(Long id)
    {
        return documentDownloadLogMapper.selectDocumentDownloadLogById(id);
    }

    /**
     * 查询文档下载日志列表
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 文档下载日志
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<DocumentDownloadLog> selectDocumentDownloadLogList(DocumentDownloadLog documentDownloadLog)
    {
        return documentDownloadLogMapper.selectDocumentDownloadLogList(documentDownloadLog);
    }

    /**
     * 新增文档下载日志
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 结果
     */
    @Override
    public int insertDocumentDownloadLog(DocumentDownloadLog documentDownloadLog)
    {
        documentDownloadLog.setDownloadTime(DateUtils.getNowDate());
        return documentDownloadLogMapper.insertDocumentDownloadLog(documentDownloadLog);
    }

    /**
     * 修改文档下载日志
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 结果
     */
    @Override
    public int updateDocumentDownloadLog(DocumentDownloadLog documentDownloadLog)
    {
        return documentDownloadLogMapper.updateDocumentDownloadLog(documentDownloadLog);
    }

    /**
     * 批量删除文档下载日志
     * 
     * @param ids 需要删除的文档下载日志主键
     * @return 结果
     */
    @Override
    public int deleteDocumentDownloadLogByIds(Long[] ids)
    {
        return documentDownloadLogMapper.deleteDocumentDownloadLogByIds(ids);
    }

    /**
     * 删除文档下载日志信息
     * 
     * @param id 文档下载日志主键
     * @return 结果
     */
    @Override
    public int deleteDocumentDownloadLogById(Long id)
    {
        return documentDownloadLogMapper.deleteDocumentDownloadLogById(id);
    }

    /**
     * 根据文档ID查询下载日志列表
     * 
     * @param documentId 文档ID
     * @return 文档下载日志集合
     */
    @Override
    public List<DocumentDownloadLog> selectDocumentDownloadLogByDocumentId(Long documentId)
    {
        return documentDownloadLogMapper.selectDocumentDownloadLogByDocumentId(documentId);
    }

    /**
     * 根据知识库ID查询下载日志列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 文档下载日志集合
     */
    @Override
    public List<DocumentDownloadLog> selectDocumentDownloadLogByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return documentDownloadLogMapper.selectDocumentDownloadLogByKnowledgeBaseId(knowledgeBaseId);
    }
}
