package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.DocumentCallLog;
import com.ruoyi.system.mapper.DocumentCallLogMapper;
import com.ruoyi.system.service.IDocumentCallLogService;

/**
 * 文档调用日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class DocumentCallLogServiceImpl implements IDocumentCallLogService
{
    @Autowired
    private DocumentCallLogMapper documentCallLogMapper;

    /**
     * 查询文档调用日志
     * 
     * @param id 文档调用日志主键
     * @return 文档调用日志
     */
    @Override
    public DocumentCallLog selectDocumentCallLogById(Long id)
    {
        return documentCallLogMapper.selectDocumentCallLogById(id);
    }

    /**
     * 查询文档调用日志列表
     * 
     * @param documentCallLog 文档调用日志
     * @return 文档调用日志
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<DocumentCallLog> selectDocumentCallLogList(DocumentCallLog documentCallLog)
    {
        return documentCallLogMapper.selectDocumentCallLogList(documentCallLog);
    }

    /**
     * 新增文档调用日志
     * 
     * @param documentCallLog 文档调用日志
     * @return 结果
     */
    @Override
    public int insertDocumentCallLog(DocumentCallLog documentCallLog)
    {
        documentCallLog.setCallTime(DateUtils.getNowDate());
        return documentCallLogMapper.insertDocumentCallLog(documentCallLog);
    }

    /**
     * 修改文档调用日志
     * 
     * @param documentCallLog 文档调用日志
     * @return 结果
     */
    @Override
    public int updateDocumentCallLog(DocumentCallLog documentCallLog)
    {
        return documentCallLogMapper.updateDocumentCallLog(documentCallLog);
    }

    /**
     * 批量删除文档调用日志
     * 
     * @param ids 需要删除的文档调用日志主键
     * @return 结果
     */
    @Override
    public int deleteDocumentCallLogByIds(Long[] ids)
    {
        return documentCallLogMapper.deleteDocumentCallLogByIds(ids);
    }

    /**
     * 删除文档调用日志信息
     * 
     * @param id 文档调用日志主键
     * @return 结果
     */
    @Override
    public int deleteDocumentCallLogById(Long id)
    {
        return documentCallLogMapper.deleteDocumentCallLogById(id);
    }

    /**
     * 根据文档ID查询调用日志列表
     * 
     * @param documentId 文档ID
     * @return 文档调用日志集合
     */
    @Override
    public List<DocumentCallLog> selectDocumentCallLogByDocumentId(Long documentId)
    {
        return documentCallLogMapper.selectDocumentCallLogByDocumentId(documentId);
    }

    /**
     * 根据知识库ID查询调用日志列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 文档调用日志集合
     */
    @Override
    public List<DocumentCallLog> selectDocumentCallLogByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return documentCallLogMapper.selectDocumentCallLogByKnowledgeBaseId(knowledgeBaseId);
    }
}
