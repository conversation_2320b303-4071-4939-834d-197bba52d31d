package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.UserGroup;

/**
 * 用户组Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IUserGroupService
{
    /**
     * 查询用户组
     * 
     * @param id 用户组主键
     * @return 用户组
     */
    public UserGroup selectUserGroupById(Long id);

    /**
     * 查询用户组列表
     * 
     * @param userGroup 用户组
     * @return 用户组集合
     */
    public List<UserGroup> selectUserGroupList(UserGroup userGroup);

    /**
     * 新增用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    public int insertUserGroup(UserGroup userGroup);

    /**
     * 修改用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    public int updateUserGroup(UserGroup userGroup);

    /**
     * 批量删除用户组
     * 
     * @param ids 需要删除的用户组主键集合
     * @return 结果
     */
    public int deleteUserGroupByIds(Long[] ids);

    /**
     * 删除用户组信息
     * 
     * @param id 用户组主键
     * @return 结果
     */
    public int deleteUserGroupById(Long id);

    /**
     * 校验用户组名称是否唯一
     * 
     * @param userGroup 用户组信息
     * @return 结果
     */
    public boolean checkUserGroupNameUnique(UserGroup userGroup);
}
