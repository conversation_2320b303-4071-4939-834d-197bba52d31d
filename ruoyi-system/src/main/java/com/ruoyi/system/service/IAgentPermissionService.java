package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AgentPermission;

/**
 * 智能体权限Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IAgentPermissionService
{
    /**
     * 查询智能体权限
     * 
     * @param id 智能体权限主键
     * @return 智能体权限
     */
    public AgentPermission selectAgentPermissionById(Long id);

    /**
     * 查询智能体权限列表
     * 
     * @param agentPermission 智能体权限
     * @return 智能体权限集合
     */
    public List<AgentPermission> selectAgentPermissionList(AgentPermission agentPermission);

    /**
     * 新增智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    public int insertAgentPermission(AgentPermission agentPermission);

    /**
     * 修改智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    public int updateAgentPermission(AgentPermission agentPermission);

    /**
     * 批量删除智能体权限
     * 
     * @param ids 需要删除的智能体权限主键集合
     * @return 结果
     */
    public int deleteAgentPermissionByIds(Long[] ids);

    /**
     * 删除智能体权限信息
     * 
     * @param id 智能体权限主键
     * @return 结果
     */
    public int deleteAgentPermissionById(Long id);

    /**
     * 批量更新智能体权限
     * 
     * @param agentPermissions 智能体权限集合
     * @param permissionType 权限类型
     * @return 结果
     */
    public int batchUpdateAgentPermission(List<AgentPermission> agentPermissions, String permissionType);
}
