package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.KnowledgeDocument;
import com.ruoyi.system.mapper.KnowledgeDocumentMapper;
import com.ruoyi.system.service.IKnowledgeDocumentService;

/**
 * 知识库文档Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class KnowledgeDocumentServiceImpl implements IKnowledgeDocumentService
{
    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;

    /**
     * 查询知识库文档
     * 
     * @param id 知识库文档主键
     * @return 知识库文档
     */
    @Override
    public KnowledgeDocument selectKnowledgeDocumentById(Long id)
    {
        return knowledgeDocumentMapper.selectKnowledgeDocumentById(id);
    }

    /**
     * 查询知识库文档列表
     * 
     * @param knowledgeDocument 知识库文档
     * @return 知识库文档
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<KnowledgeDocument> selectKnowledgeDocumentList(KnowledgeDocument knowledgeDocument)
    {
        return knowledgeDocumentMapper.selectKnowledgeDocumentList(knowledgeDocument);
    }

    /**
     * 新增知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    @Override
    public int insertKnowledgeDocument(KnowledgeDocument knowledgeDocument)
    {
        knowledgeDocument.setCreateTime(DateUtils.getNowDate());
        knowledgeDocument.setUploadUserId(SecurityUtils.getUserId());
        knowledgeDocument.setUploadUserName(SecurityUtils.getUsername());
        return knowledgeDocumentMapper.insertKnowledgeDocument(knowledgeDocument);
    }

    /**
     * 修改知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    @Override
    public int updateKnowledgeDocument(KnowledgeDocument knowledgeDocument)
    {
        knowledgeDocument.setUpdateTime(DateUtils.getNowDate());
        return knowledgeDocumentMapper.updateKnowledgeDocument(knowledgeDocument);
    }

    /**
     * 批量删除知识库文档
     * 
     * @param ids 需要删除的知识库文档主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeDocumentByIds(Long[] ids)
    {
        return knowledgeDocumentMapper.deleteKnowledgeDocumentByIds(ids);
    }

    /**
     * 删除知识库文档信息
     * 
     * @param id 知识库文档主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeDocumentById(Long id)
    {
        return knowledgeDocumentMapper.deleteKnowledgeDocumentById(id);
    }

    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文档集合
     */
    @Override
    public List<KnowledgeDocument> selectKnowledgeDocumentByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return knowledgeDocumentMapper.selectKnowledgeDocumentByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 上传文档到知识库
     * 
     * @param knowledgeBaseId 知识库ID
     * @param files 文件列表
     * @return 结果
     */
    @Override
    public int uploadDocuments(Long knowledgeBaseId, MultipartFile[] files)
    {
        int successCount = 0;
        
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }
            
            try {
                // 创建知识库文档对象
                KnowledgeDocument document = new KnowledgeDocument();
                document.setKnowledgeBaseId(knowledgeBaseId);
                document.setFileName(file.getOriginalFilename());
                document.setFileType(getFileExtension(file.getOriginalFilename()));
                document.setFileSize(file.getSize());
                document.setStatus("0"); // 0-未解析, 1-解析中, 2-已解析, 3-解析失败
                document.setParseProgress(0);
                
                // 这里可以保存文件到本地或云存储
                // 暂时设置一个临时路径
                String filePath = "/uploads/knowledge/" + knowledgeBaseId + "/" + file.getOriginalFilename();
                document.setFilePath(filePath);
                
                // 保存到数据库
                if (insertKnowledgeDocument(document) > 0) {
                    successCount++;
                }
                
            } catch (Exception e) {
                // 记录错误日志
                e.printStackTrace();
            }
        }
        
        return successCount;
    }

    /**
     * 解析文档
     * 
     * @param documentId 文档ID
     * @return 结果
     */
    @Override
    public int parseDocument(Long documentId)
    {
        try {
            // KnowledgeDocument document = new KnowledgeDocument();
            // document.setId(documentId);
            // document.setStatus("1"); // 解析中
            // document.setParseProgress(0);
            // updateKnowledgeDocument(document);
            
            // 异步调用Python端解析服务
            asyncParseDocument(documentId);
            
            return 1; // 返回成功，表示解析任务已提交
            
        } catch (Exception e) {
            // 解析失败，更新状态
            KnowledgeDocument document = new KnowledgeDocument();
            document.setId(documentId);
            document.setStatus("3"); // 解析失败
            document.setParseResult("解析失败: " + e.getMessage());
            updateKnowledgeDocument(document);
            return 0;
        }
    }
    
    /**
     * 异步解析文档
     * 
     * @param documentId 文档ID
     */
    private void asyncParseDocument(Long documentId) {
        // 使用线程池异步执行解析任务
        Thread.startVirtualThread(() -> {
            try {
                // 调用Python端解析服务
                callPythonParseService(documentId);
            } catch (Exception e) {
                // 解析失败，更新状态
                KnowledgeDocument document = new KnowledgeDocument();
                document.setId(documentId);
                document.setStatus("3"); // 解析失败
                document.setParseResult("解析失败: " + e.getMessage());
                updateKnowledgeDocument(document);
            }
        });
    }
    
    /**
     * 调用Python端解析服务
     * 
     * @param documentId 文档ID
     */
    private void callPythonParseService(Long documentId) throws Exception {
        // 构建请求URL
        String url = "http://127.0.0.1:1025/document/parse/" + documentId+"?force=true";
        
        try {
            // 发送HTTP请求
            java.net.http.HttpClient client = java.net.http.HttpClient.newHttpClient();
            
            java.net.http.HttpRequest request = java.net.http.HttpRequest.newBuilder()
                    .uri(java.net.URI.create(url))
                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.noBody()) // 空请求体
                    .timeout(java.time.Duration.ofSeconds(30)) // 30秒超时，因为是异步处理
                    .build();
            
            java.net.http.HttpResponse<String> response = client.send(request, 
                    java.net.http.HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() != 200) {
                throw new RuntimeException("Python服务调用失败: HTTP " + response.statusCode() + ", " + response.body());
            }
            
            // 解析响应
            com.alibaba.fastjson2.JSONObject responseObj = com.alibaba.fastjson2.JSON.parseObject(response.body());
            Boolean success = responseObj.getBoolean("success");
            
            if (success == null || !success) {
                throw new RuntimeException("Python服务返回失败: " + response.body());
            }
            
            // 记录成功日志
            System.out.println("成功提交文档解析任务，文档ID: " + documentId);
            
        } catch (Exception e) {
            System.err.println("调用Python服务失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 批量解析文档
     * 
     * @param ids 需要解析的文档ID数组
     * @return 结果
     */
    @Override
    public int batchParseDocuments(Long[] ids)
    {
        int successCount = 0;
        for (Long id : ids) {
            if (parseDocument(id) > 0) {
                successCount++;
            }
        }
        return successCount;
    }

    /**
     * 更新文档解析状态
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    @Override
    public int updateParseStatus(KnowledgeDocument knowledgeDocument)
    {
        return knowledgeDocumentMapper.updateKnowledgeDocument(knowledgeDocument);
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
}
