package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.KnowledgeBase;
import com.ruoyi.system.mapper.KnowledgeBaseMapper;
import com.ruoyi.system.service.IKnowledgeBaseService;

/**
 * 知识库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class KnowledgeBaseServiceImpl implements IKnowledgeBaseService
{
    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 查询知识库
     * 
     * @param id 知识库主键
     * @return 知识库
     */
    @Override
    public KnowledgeBase selectKnowledgeBaseById(Long id)
    {
        return knowledgeBaseMapper.selectKnowledgeBaseById(id);
    }

    /**
     * 查询知识库列表
     * 
     * @param knowledgeBase 知识库
     * @return 知识库
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<KnowledgeBase> selectKnowledgeBaseList(KnowledgeBase knowledgeBase)
    {
        return knowledgeBaseMapper.selectKnowledgeBaseList(knowledgeBase);
    }

    /**
     * 新增知识库
     * 
     * @param knowledgeBase 知识库
     * @return 结果
     */
    @Override
    public int insertKnowledgeBase(KnowledgeBase knowledgeBase)
    {
        knowledgeBase.setCreateTime(DateUtils.getNowDate());
        knowledgeBase.setUpdateTime(DateUtils.getNowDate());
        knowledgeBase.setCreateUserId(SecurityUtils.getUserId());
        knowledgeBase.setCreateUserName(SecurityUtils.getUsername());
        if (StringUtils.isNull(knowledgeBase.getStatus()))
        {
            knowledgeBase.setStatus("0");
        }
        if (StringUtils.isNull(knowledgeBase.getDocumentCount()))
        {
            knowledgeBase.setDocumentCount(0L);
        }
        return knowledgeBaseMapper.insertKnowledgeBase(knowledgeBase);
    }

    /**
     * 修改知识库
     * 
     * @param knowledgeBase 知识库
     * @return 结果
     */
    @Override
    public int updateKnowledgeBase(KnowledgeBase knowledgeBase)
    {
        knowledgeBase.setUpdateTime(DateUtils.getNowDate());
        return knowledgeBaseMapper.updateKnowledgeBase(knowledgeBase);
    }

    /**
     * 批量删除知识库
     * 
     * @param ids 需要删除的知识库主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBaseByIds(Long[] ids)
    {
        return knowledgeBaseMapper.deleteKnowledgeBaseByIds(ids);
    }

    /**
     * 删除知识库信息
     * 
     * @param id 知识库主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeBaseById(Long id)
    {
        return knowledgeBaseMapper.deleteKnowledgeBaseById(id);
    }

    /**
     * 校验知识库名称是否唯一
     * 
     * @param knowledgeBase 知识库信息
     * @return 结果
     */
    @Override
    public boolean checkKnowledgeBaseNameUnique(KnowledgeBase knowledgeBase)
    {
        Long knowledgeBaseId = StringUtils.isNull(knowledgeBase.getId()) ? -1L : knowledgeBase.getId();
        KnowledgeBase info = knowledgeBaseMapper.checkKnowledgeBaseNameUnique(knowledgeBase);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != knowledgeBaseId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
}
