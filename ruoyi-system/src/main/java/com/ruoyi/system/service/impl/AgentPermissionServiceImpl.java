package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AgentPermissionMapper;
import com.ruoyi.system.domain.AgentPermission;
import com.ruoyi.system.service.IAgentPermissionService;

/**
 * 智能体权限Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class AgentPermissionServiceImpl implements IAgentPermissionService
{
    @Autowired
    private AgentPermissionMapper agentPermissionMapper;

    /**
     * 查询智能体权限
     * 
     * @param id 智能体权限主键
     * @return 智能体权限
     */
    @Override
    public AgentPermission selectAgentPermissionById(Long id)
    {
        return agentPermissionMapper.selectAgentPermissionById(id);
    }

    /**
     * 查询智能体权限列表
     * 
     * @param agentPermission 智能体权限
     * @return 智能体权限
     */
    @Override
    public List<AgentPermission> selectAgentPermissionList(AgentPermission agentPermission)
    {
        return agentPermissionMapper.selectAgentPermissionList(agentPermission);
    }

    /**
     * 新增智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    @Override
    public int insertAgentPermission(AgentPermission agentPermission)
    {
        agentPermission.setCreateTime(DateUtils.getNowDate());
        return agentPermissionMapper.insertAgentPermission(agentPermission);
    }

    /**
     * 修改智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    @Override
    public int updateAgentPermission(AgentPermission agentPermission)
    {
        agentPermission.setUpdateTime(DateUtils.getNowDate());
        return agentPermissionMapper.updateAgentPermission(agentPermission);
    }

    /**
     * 批量删除智能体权限
     * 
     * @param ids 需要删除的智能体权限主键
     * @return 结果
     */
    @Override
    public int deleteAgentPermissionByIds(Long[] ids)
    {
        return agentPermissionMapper.deleteAgentPermissionByIds(ids);
    }

    /**
     * 删除智能体权限信息
     * 
     * @param id 智能体权限主键
     * @return 结果
     */
    @Override
    public int deleteAgentPermissionById(Long id)
    {
        return agentPermissionMapper.deleteAgentPermissionById(id);
    }

    /**
     * 批量更新智能体权限
     * 
     * @param agentPermissions 智能体权限集合
     * @param permissionType 权限类型
     * @return 结果
     */
    @Override
    public int batchUpdateAgentPermission(List<AgentPermission> agentPermissions, String permissionType)
    {
        if (agentPermissions == null || agentPermissions.isEmpty()) {
            return 0;
        }
        
        // 获取目标ID（机构ID或用户组ID）
        Long targetId = agentPermissions.get(0).getTargetId();
        
        // 先删除该目标的所有权限
        AgentPermission deleteParam = new AgentPermission();
        deleteParam.setTargetId(targetId);
        deleteParam.setPermissionType(permissionType);
        List<AgentPermission> existingPermissions = agentPermissionMapper.selectAgentPermissionList(deleteParam);
        if (!existingPermissions.isEmpty()) {
            Long[] ids = existingPermissions.stream().map(AgentPermission::getId).toArray(Long[]::new);
            agentPermissionMapper.deleteAgentPermissionByIds(ids);
        }
        
        // 批量新增权限
        for (AgentPermission permission : agentPermissions) {
            permission.setCreateTime(DateUtils.getNowDate());
        }
        return agentPermissionMapper.batchInsertAgentPermission(agentPermissions);
    }
}
