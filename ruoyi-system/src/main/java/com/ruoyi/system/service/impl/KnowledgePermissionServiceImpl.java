package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.KnowledgeBase;
import com.ruoyi.system.domain.KnowledgeDocument;
import com.ruoyi.system.domain.KnowledgePermission;
import com.ruoyi.system.domain.KnowledgePermissionTreeNode;
import com.ruoyi.system.mapper.KnowledgeBaseMapper;
import com.ruoyi.system.mapper.KnowledgeDocumentMapper;
import com.ruoyi.system.mapper.KnowledgePermissionMapper;
import com.ruoyi.system.service.IKnowledgePermissionService;

/**
 * 知识库权限Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class KnowledgePermissionServiceImpl implements IKnowledgePermissionService
{
    @Autowired
    private KnowledgePermissionMapper knowledgePermissionMapper;

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;

    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    @Override
    public KnowledgePermission selectKnowledgePermissionById(Long id)
    {
        return knowledgePermissionMapper.selectKnowledgePermissionById(id);
    }

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgePermission 知识库权限
     * @return 知识库权限
     */
    @Override
    public List<KnowledgePermission> selectKnowledgePermissionList(KnowledgePermission knowledgePermission)
    {
        return knowledgePermissionMapper.selectKnowledgePermissionList(knowledgePermission);
    }

    /**
     * 新增知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    @Override
    public int insertKnowledgePermission(KnowledgePermission knowledgePermission)
    {
        knowledgePermission.setCreateTime(DateUtils.getNowDate());
        return knowledgePermissionMapper.insertKnowledgePermission(knowledgePermission);
    }

    /**
     * 修改知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    @Override
    public int updateKnowledgePermission(KnowledgePermission knowledgePermission)
    {
        knowledgePermission.setUpdateTime(DateUtils.getNowDate());
        return knowledgePermissionMapper.updateKnowledgePermission(knowledgePermission);
    }

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的知识库权限主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgePermissionByIds(Long[] ids)
    {
        return knowledgePermissionMapper.deleteKnowledgePermissionByIds(ids);
    }

    /**
     * 删除知识库权限信息
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgePermissionById(Long id)
    {
        return knowledgePermissionMapper.deleteKnowledgePermissionById(id);
    }

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    @Override
    public List<KnowledgePermission> selectKnowledgePermissionByBaseId(Long knowledgeBaseId)
    {
        KnowledgePermission knowledgePermission = new KnowledgePermission();
        knowledgePermission.setKnowledgeBaseId(knowledgeBaseId);
        return knowledgePermissionMapper.selectKnowledgePermissionList(knowledgePermission);
    }

    /**
     * 根据知识库ID和权限类型查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @param permissionType 权限类型
     * @return 知识库权限集合
     */
    @Override
    public List<KnowledgePermission> selectKnowledgePermissionByBaseIdAndType(Long knowledgeBaseId, String permissionType)
    {
        KnowledgePermission knowledgePermission = new KnowledgePermission();
        knowledgePermission.setKnowledgeBaseId(knowledgeBaseId);
        knowledgePermission.setPermissionType(permissionType);
        return knowledgePermissionMapper.selectKnowledgePermissionList(knowledgePermission);
    }

    /**
     * 批量更新知识库权限
     * 
     * @param knowledgePermissions 知识库权限集合
     * @param permissionType 权限类型
     * @return 结果
     */
    @Override
    public int batchUpdateKnowledgePermission(List<KnowledgePermission> knowledgePermissions, String permissionType)
    {
        if (knowledgePermissions == null || knowledgePermissions.isEmpty()) {
            return 0;
        }
        
        // 获取目标ID（机构ID或用户组ID）
        Long targetId = knowledgePermissions.get(0).getTargetId();
        
        // 先删除该目标的所有权限
        KnowledgePermission deleteParam = new KnowledgePermission();
        deleteParam.setTargetId(targetId);
        deleteParam.setPermissionType(permissionType);
        List<KnowledgePermission> existingPermissions = knowledgePermissionMapper.selectKnowledgePermissionList(deleteParam);
        if (!existingPermissions.isEmpty()) {
            Long[] ids = existingPermissions.stream().map(KnowledgePermission::getId).toArray(Long[]::new);
            knowledgePermissionMapper.deleteKnowledgePermissionByIds(ids);
        }
        
        // 批量新增权限
        for (KnowledgePermission permission : knowledgePermissions) {
            permission.setCreateTime(DateUtils.getNowDate());
        }
        return knowledgePermissionMapper.batchInsertKnowledgePermission(knowledgePermissions);
    }

    /**
     * 获取文档权限树（优化版：知识库节点可选择，用于前端全选功能）
     * 
     * @return 权限树节点集合
     */
    @Override
    public List<KnowledgePermissionTreeNode> getKnowledgePermissionTree()
    {
        List<KnowledgePermissionTreeNode> result = new ArrayList<>();
        
        // 获取所有知识库
        List<KnowledgeBase> knowledgeBases = knowledgeBaseMapper.selectKnowledgeBaseList(new KnowledgeBase());
        
        for (KnowledgeBase kb : knowledgeBases) {
            // 创建知识库节点（可选择，用于前端全选功能）
            KnowledgePermissionTreeNode kbNode = new KnowledgePermissionTreeNode();
            kbNode.setId("kb_" + kb.getId());
            kbNode.setName(kb.getName());
            kbNode.setType("knowledge_base");
            kbNode.setRealId(kb.getId());
            kbNode.setSelectable(true); // 知识库节点可选择，但仅用于前端全选功能
            
            // 获取该知识库下的文档
            KnowledgeDocument docQuery = new KnowledgeDocument();
            docQuery.setKnowledgeBaseId(kb.getId());
            List<KnowledgeDocument> documents = knowledgeDocumentMapper.selectKnowledgeDocumentList(docQuery);
            
            List<KnowledgePermissionTreeNode> docNodes = new ArrayList<>();
            for (KnowledgeDocument doc : documents) {
                KnowledgePermissionTreeNode docNode = new KnowledgePermissionTreeNode();
                // 优化：直接使用文档ID，去掉前缀
                docNode.setId(doc.getId().toString());
                docNode.setName(doc.getFileName());
                docNode.setType("document");
                docNode.setRealId(doc.getId());
                docNode.setKnowledgeBaseId(kb.getId());
                docNode.setParentId("kb_" + kb.getId());
                docNode.setSelectable(true); // 文档节点可选中
                docNodes.add(docNode);
            }
            
            // 只有当知识库下有文档时才添加知识库节点
            if (!docNodes.isEmpty()) {
                kbNode.setChildren(docNodes);
                result.add(kbNode);
            }
        }
        
        return result;
    }

    /**
     * 获取机构的权限节点ID列表
     * 
     * @param orgId 机构ID
     * @return 权限节点ID列表
     */
    @Override
    public List<String> getOrgPermissionNodeIds(Long orgId)
    {
        return getPermissionNodeIds(orgId, "org");
    }

    /**
     * 获取用户组的权限节点ID列表
     * 
     * @param userGroupId 用户组ID
     * @return 权限节点ID列表
     */
    @Override
    public List<String> getUserGroupPermissionNodeIds(Long userGroupId)
    {
        return getPermissionNodeIds(userGroupId, "user_group");
    }

    /**
     * 获取权限节点ID列表（优化版：只返回文档ID，不包含前缀）
     * 
     * @param targetId 目标ID
     * @param permissionType 权限类型
     * @return 权限节点ID列表
     */
    private List<String> getPermissionNodeIds(Long targetId, String permissionType)
    {
        KnowledgePermission query = new KnowledgePermission();
        query.setTargetId(targetId);
        query.setPermissionType(permissionType);
        
        List<KnowledgePermission> permissions = knowledgePermissionMapper.selectKnowledgePermissionList(query);
        
        List<String> nodeIds = new ArrayList<>();
        for (KnowledgePermission permission : permissions) {
            // 只返回有文档权限的记录，去掉知识库级别权限
            if (permission.getDocumentId() != null) {
                // 优化：直接返回文档ID，不加前缀
                nodeIds.add(permission.getDocumentId().toString());
            }
        }
        
        return nodeIds;
    }

    /**
     * 批量更新机构权限（支持文档级别）
     * 
     * @param orgId 机构ID
     * @param orgName 机构名称
     * @param nodeIds 权限节点ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateOrgPermissions(Long orgId, String orgName, List<String> nodeIds)
    {
        return batchUpdatePermissions(orgId, orgName, "org", nodeIds);
    }

    /**
     * 批量更新用户组权限（支持文档级别）
     * 
     * @param userGroupId 用户组ID
     * @param userGroupName 用户组名称
     * @param nodeIds 权限节点ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateUserGroupPermissions(Long userGroupId, String userGroupName, List<String> nodeIds)
    {
        return batchUpdatePermissions(userGroupId, userGroupName, "user_group", nodeIds);
    }

    /**
     * 批量更新权限（优化版：只支持文档级别权限）
     * 
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param permissionType 权限类型
     * @param nodeIds 文档ID列表（不包含前缀）
     * @return 结果
     */
    private int batchUpdatePermissions(Long targetId, String targetName, String permissionType, List<String> nodeIds)
    {
        // 先删除该目标的所有权限
        KnowledgePermission deleteParam = new KnowledgePermission();
        deleteParam.setTargetId(targetId);
        deleteParam.setPermissionType(permissionType);
        List<KnowledgePermission> existingPermissions = knowledgePermissionMapper.selectKnowledgePermissionList(deleteParam);
        if (!existingPermissions.isEmpty()) {
            Long[] ids = existingPermissions.stream().map(KnowledgePermission::getId).toArray(Long[]::new);
            knowledgePermissionMapper.deleteKnowledgePermissionByIds(ids);
        }

        // 没有选择任何权限，直接返回
        if (nodeIds == null || nodeIds.isEmpty()) {
            return 1;
        }

        // 构造新的权限列表（只支持文档级别权限）
        List<KnowledgePermission> newPermissions = new ArrayList<>();
        Date now = DateUtils.getNowDate();

        for (String documentIdStr : nodeIds) {
            try {
                Long documentId = Long.parseLong(documentIdStr);
                
                // 获取文档信息以获取知识库ID
                KnowledgeDocument document = knowledgeDocumentMapper.selectKnowledgeDocumentById(documentId);
                if (document != null) {
                    KnowledgePermission permission = new KnowledgePermission();
                    permission.setTargetId(targetId);
                    permission.setTargetName(targetName);
                    permission.setPermissionType(permissionType);
                    permission.setPermissionLevel("read"); // 默认只读权限
                    permission.setStatus("0");
                    permission.setCreateTime(now);
                    
                    // 设置为文档级别权限
                    permission.setKnowledgeBaseId(document.getKnowledgeBaseId());
                    permission.setDocumentId(documentId);
                    
                    newPermissions.add(permission);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的文档ID
                continue;
            }
        }

        // 批量插入新权限
        if (!newPermissions.isEmpty()) {
            return knowledgePermissionMapper.batchInsertKnowledgePermission(newPermissions);
        }

        return 1;
    }
}
