package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.DocumentDownloadLog;

/**
 * 文档下载日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IDocumentDownloadLogService
{
    /**
     * 查询文档下载日志
     * 
     * @param id 文档下载日志主键
     * @return 文档下载日志
     */
    public DocumentDownloadLog selectDocumentDownloadLogById(Long id);

    /**
     * 查询文档下载日志列表
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 文档下载日志集合
     */
    public List<DocumentDownloadLog> selectDocumentDownloadLogList(DocumentDownloadLog documentDownloadLog);

    /**
     * 新增文档下载日志
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 结果
     */
    public int insertDocumentDownloadLog(DocumentDownloadLog documentDownloadLog);

    /**
     * 修改文档下载日志
     * 
     * @param documentDownloadLog 文档下载日志
     * @return 结果
     */
    public int updateDocumentDownloadLog(DocumentDownloadLog documentDownloadLog);

    /**
     * 批量删除文档下载日志
     * 
     * @param ids 需要删除的文档下载日志主键集合
     * @return 结果
     */
    public int deleteDocumentDownloadLogByIds(Long[] ids);

    /**
     * 删除文档下载日志信息
     * 
     * @param id 文档下载日志主键
     * @return 结果
     */
    public int deleteDocumentDownloadLogById(Long id);

    /**
     * 根据文档ID查询下载日志列表
     * 
     * @param documentId 文档ID
     * @return 文档下载日志集合
     */
    public List<DocumentDownloadLog> selectDocumentDownloadLogByDocumentId(Long documentId);

    /**
     * 根据知识库ID查询下载日志列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 文档下载日志集合
     */
    public List<DocumentDownloadLog> selectDocumentDownloadLogByKnowledgeBaseId(Long knowledgeBaseId);
}
