package com.ruoyi.system.domain;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库文档对象 knowledge_document
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class KnowledgeDocument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 文档名称 */
    @Excel(name = "文档名称")
    @NotBlank(message = "文档名称不能为空")
    @Size(min = 0, max = 200, message = "文档名称长度不能超过200个字符")
    private String fileName;

    /** 文档类型 */
    @Excel(name = "文档类型")
    private String fileType;

    /** 文件大小(字节) */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文档状态(0-未解析,1-解析中,2-解析完成,3-解析失败) */
    @Excel(name = "文档状态", readConverterExp = "0=未解析,1=解析中,2=解析完成,3=解析失败")
    private String status;

    /** 解析进度 */
    @Excel(name = "解析进度")
    private Integer parseProgress;

    /** 解析结果 */
    @Excel(name = "解析结果")
    private String parseResult;

    /** 上传用户ID */
    @Excel(name = "上传用户ID")
    private Long uploadUserId;

    /** 上传用户名 */
    @Excel(name = "上传用户名")
    private String uploadUserName;

    /** 下载次数 */
    @Excel(name = "下载次数")
    private Long downloadCount;

    /** 最后下载时间 */
    @Excel(name = "最后下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastDownloadTime;

    /** 调用次数 */
    @Excel(name = "调用次数")
    private Long callCount;

    /** 最后调用时间 */
    @Excel(name = "最后调用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastCallTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }

    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setParseProgress(Integer parseProgress) 
    {
        this.parseProgress = parseProgress;
    }

    public Integer getParseProgress() 
    {
        return parseProgress;
    }

    public void setParseResult(String parseResult) 
    {
        this.parseResult = parseResult;
    }

    public String getParseResult() 
    {
        return parseResult;
    }

    public void setUploadUserId(Long uploadUserId) 
    {
        this.uploadUserId = uploadUserId;
    }

    public Long getUploadUserId() 
    {
        return uploadUserId;
    }

    public void setUploadUserName(String uploadUserName) 
    {
        this.uploadUserName = uploadUserName;
    }

    public String getUploadUserName() 
    {
        return uploadUserName;
    }

    public void setDownloadCount(Long downloadCount) 
    {
        this.downloadCount = downloadCount;
    }

    public Long getDownloadCount() 
    {
        return downloadCount;
    }

    public void setLastDownloadTime(Date lastDownloadTime) 
    {
        this.lastDownloadTime = lastDownloadTime;
    }

    public Date getLastDownloadTime() 
    {
        return lastDownloadTime;
    }

    public void setCallCount(Long callCount) 
    {
        this.callCount = callCount;
    }

    public Long getCallCount() 
    {
        return callCount;
    }

    public void setLastCallTime(Date lastCallTime) 
    {
        this.lastCallTime = lastCallTime;
    }

    public Date getLastCallTime() 
    {
        return lastCallTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("fileName", getFileName())
            .append("fileType", getFileType())
            .append("fileSize", getFileSize())
            .append("filePath", getFilePath())
            .append("status", getStatus())
            .append("parseProgress", getParseProgress())
            .append("parseResult", getParseResult())
            .append("uploadUserId", getUploadUserId())
            .append("uploadUserName", getUploadUserName())
            .append("downloadCount", getDownloadCount())
            .append("lastDownloadTime", getLastDownloadTime())
            .append("callCount", getCallCount())
            .append("lastCallTime", getLastCallTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
