package com.ruoyi.system.domain;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户组对象 user_group
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class UserGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户组ID */
    private Long id;

    /** 用户组名称 */
    @Excel(name = "用户组名称")
    @NotBlank(message = "用户组名称不能为空")
    @Size(min = 0, max = 50, message = "用户组名称长度不能超过50个字符")
    private String name;

    /** 用户组描述 */
    @Excel(name = "用户组描述")
    @Size(min = 0, max = 500, message = "用户组描述长度不能超过500个字符")
    private String description;

    /** 所属机构ID */
    @Excel(name = "所属机构ID")
    private Long orgId;

    /** 所属机构名称 */
    @Excel(name = "所属机构名称")
    private String orgName;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private Long createUserId;

    /** 创建用户名 */
    @Excel(name = "创建用户名")
    private String createUserName;

    /** 状态(0-正常,1-停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 成员数量 */
    @Excel(name = "成员数量")
    private Integer memberCount;

    /** 用户组成员列表 */
    private List<UserGroupMember> members;

    /** 用户ID数组 */
    private Long[] userIds;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setOrgId(Long orgId) 
    {
        this.orgId = orgId;
    }

    public Long getOrgId() 
    {
        return orgId;
    }

    public void setOrgName(String orgName) 
    {
        this.orgName = orgName;
    }

    public String getOrgName() 
    {
        return orgName;
    }

    public void setCreateUserId(Long createUserId) 
    {
        this.createUserId = createUserId;
    }

    public Long getCreateUserId() 
    {
        return createUserId;
    }

    public void setCreateUserName(String createUserName) 
    {
        this.createUserName = createUserName;
    }

    public String getCreateUserName() 
    {
        return createUserName;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setMemberCount(Integer memberCount) 
    {
        this.memberCount = memberCount;
    }

    public Integer getMemberCount() 
    {
        return memberCount;
    }

    public void setMembers(List<UserGroupMember> members) 
    {
        this.members = members;
    }

    public List<UserGroupMember> getMembers() 
    {
        return members;
    }

    public void setUserIds(Long[] userIds) 
    {
        this.userIds = userIds;
    }

    public Long[] getUserIds() 
    {
        return userIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("description", getDescription())
            .append("orgId", getOrgId())
            .append("orgName", getOrgName())
            .append("createUserId", getCreateUserId())
            .append("createUserName", getCreateUserName())
            .append("status", getStatus())
            .append("memberCount", getMemberCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
