package com.ruoyi.system.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文档下载日志对象 document_download_log
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class DocumentDownloadLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long id;

    /** 文档ID */
    @Excel(name = "文档ID")
    private Long documentId;

    /** 文档名称 */
    @Excel(name = "文档名称")
    private String documentName;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    private String knowledgeBaseName;

    /** 下载用户ID */
    @Excel(name = "下载用户ID")
    private Long downloadUserId;

    /** 下载用户名 */
    @Excel(name = "下载用户名")
    private String downloadUserName;

    /** 下载时间 */
    @Excel(name = "下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date downloadTime;

    /** 下载方式(direct-直接下载,batch-批量下载) */
    @Excel(name = "下载方式", readConverterExp = "direct=直接下载,batch=批量下载")
    private String downloadType;

    /** 下载结果(success-成功,failure-失败) */
    @Excel(name = "下载结果", readConverterExp = "success=成功,failure=失败")
    private String downloadResult;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 下载IP */
    @Excel(name = "下载IP")
    private String downloadIp;

    /** 文件大小(字节) */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 下载时长(毫秒) */
    @Excel(name = "下载时长")
    private Long downloadDuration;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDocumentId(Long documentId) 
    {
        this.documentId = documentId;
    }

    public Long getDocumentId() 
    {
        return documentId;
    }

    public void setDocumentName(String documentName) 
    {
        this.documentName = documentName;
    }

    public String getDocumentName() 
    {
        return documentName;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseName(String knowledgeBaseName) 
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName() 
    {
        return knowledgeBaseName;
    }

    public void setDownloadUserId(Long downloadUserId) 
    {
        this.downloadUserId = downloadUserId;
    }

    public Long getDownloadUserId() 
    {
        return downloadUserId;
    }

    public void setDownloadUserName(String downloadUserName) 
    {
        this.downloadUserName = downloadUserName;
    }

    public String getDownloadUserName() 
    {
        return downloadUserName;
    }

    public void setDownloadTime(Date downloadTime) 
    {
        this.downloadTime = downloadTime;
    }

    public Date getDownloadTime() 
    {
        return downloadTime;
    }

    public void setDownloadType(String downloadType) 
    {
        this.downloadType = downloadType;
    }

    public String getDownloadType() 
    {
        return downloadType;
    }

    public void setDownloadResult(String downloadResult) 
    {
        this.downloadResult = downloadResult;
    }

    public String getDownloadResult() 
    {
        return downloadResult;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setDownloadIp(String downloadIp) 
    {
        this.downloadIp = downloadIp;
    }

    public String getDownloadIp() 
    {
        return downloadIp;
    }

    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }

    public void setDownloadDuration(Long downloadDuration) 
    {
        this.downloadDuration = downloadDuration;
    }

    public Long getDownloadDuration() 
    {
        return downloadDuration;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("documentId", getDocumentId())
            .append("documentName", getDocumentName())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("downloadUserId", getDownloadUserId())
            .append("downloadUserName", getDownloadUserName())
            .append("downloadTime", getDownloadTime())
            .append("downloadType", getDownloadType())
            .append("downloadResult", getDownloadResult())
            .append("errorMessage", getErrorMessage())
            .append("downloadIp", getDownloadIp())
            .append("fileSize", getFileSize())
            .append("downloadDuration", getDownloadDuration())
            .toString();
    }
}
