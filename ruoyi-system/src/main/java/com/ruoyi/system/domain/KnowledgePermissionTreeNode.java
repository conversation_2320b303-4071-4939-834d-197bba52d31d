package com.ruoyi.system.domain;

import java.util.List;
import java.util.ArrayList;

/**
 * 知识库权限树节点对象
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class KnowledgePermissionTreeNode
{
    /** 节点ID */
    private String id;

    /** 节点名称 */
    private String name;

    /** 节点类型 knowledge_base-知识库, document-文档 */
    private String type;

    /** 实际ID（知识库ID或文档ID） */
    private Long realId;

    /** 知识库ID（文档节点时使用） */
    private Long knowledgeBaseId;

    /** 父节点ID */
    private String parentId;

    /** 子节点列表 */
    private List<KnowledgePermissionTreeNode> children;

    /** 是否选中 */
    private boolean checked = false;

    /** 是否可选择（用于区分分组节点和可选节点） */
    private boolean selectable = true;

    public KnowledgePermissionTreeNode()
    {
        this.children = new ArrayList<KnowledgePermissionTreeNode>();
    }

    public KnowledgePermissionTreeNode(String id, String name, String type, Long realId)
    {
        this.id = id;
        this.name = name;
        this.type = type;
        this.realId = realId;
        this.children = new ArrayList<KnowledgePermissionTreeNode>();
    }

    public String getId()
    {
        return id;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public Long getRealId()
    {
        return realId;
    }

    public void setRealId(Long realId)
    {
        this.realId = realId;
    }

    public Long getKnowledgeBaseId()
    {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId)
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public String getParentId()
    {
        return parentId;
    }

    public void setParentId(String parentId)
    {
        this.parentId = parentId;
    }

    public List<KnowledgePermissionTreeNode> getChildren()
    {
        return children;
    }

    public void setChildren(List<KnowledgePermissionTreeNode> children)
    {
        this.children = children;
    }

    public boolean isChecked()
    {
        return checked;
    }

    public void setChecked(boolean checked)
    {
        this.checked = checked;
    }

    public boolean isSelectable()
    {
        return selectable;
    }

    public void setSelectable(boolean selectable)
    {
        this.selectable = selectable;
    }
}