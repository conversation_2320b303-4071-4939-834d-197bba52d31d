package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库权限对象 knowledge_permission
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class KnowledgePermission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 权限ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 文档ID */
    @Excel(name = "文档ID")
    private Long documentId;

    /** 权限类型(org-机构,user_group-用户组) */
    @Excel(name = "权限类型", readConverterExp = "org=机构,user_group=用户组")
    private String permissionType;

    /** 权限目标ID(机构ID或用户组ID) */
    @Excel(name = "权限目标ID")
    private Long targetId;

    /** 权限目标名称 */
    @Excel(name = "权限目标名称")
    private String targetName;

    /** 权限级别(read-只读,write-读写,admin-管理) */
    @Excel(name = "权限级别", readConverterExp = "read=只读,write=读写,admin=管理")
    private String permissionLevel;

    /** 状态(0-正常,1-停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setDocumentId(Long documentId) 
    {
        this.documentId = documentId;
    }

    public Long getDocumentId() 
    {
        return documentId;
    }

    public void setPermissionType(String permissionType) 
    {
        this.permissionType = permissionType;
    }

    public String getPermissionType() 
    {
        return permissionType;
    }

    public void setTargetId(Long targetId) 
    {
        this.targetId = targetId;
    }

    public Long getTargetId() 
    {
        return targetId;
    }

    public void setTargetName(String targetName) 
    {
        this.targetName = targetName;
    }

    public String getTargetName() 
    {
        return targetName;
    }

    public void setPermissionLevel(String permissionLevel) 
    {
        this.permissionLevel = permissionLevel;
    }

    public String getPermissionLevel() 
    {
        return permissionLevel;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("documentId", getDocumentId())
            .append("permissionType", getPermissionType())
            .append("targetId", getTargetId())
            .append("targetName", getTargetName())
            .append("permissionLevel", getPermissionLevel())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
