package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 智能体对象 agent_config
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class AgentConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 智能体ID */
    private Long id;

    /** 智能体名称 */
    @Excel(name = "智能体名称")
    @NotBlank(message = "智能体名称不能为空")
    @Size(min = 0, max = 50, message = "智能体名称长度不能超过50个字符")
    private String name;

    /** API密钥 */
    @Excel(name = "API密钥")
    @NotBlank(message = "API密钥不能为空")
    @Size(min = 0, max = 200, message = "API密钥长度不能超过200个字符")
    private String apiKey;

    /** 模型名称 */
    @Excel(name = "模型名称")
    @NotBlank(message = "模型名称不能为空")
    @Size(min = 0, max = 100, message = "模型名称长度不能超过100个字符")
    private String model;

    /** 模型温度参数 */
    @Excel(name = "模型温度参数")
    private Double temperature;

    /** 最大Token数 */
    @Excel(name = "最大Token数")
    private Integer maxTokens;

    /** 智能体描述 */
    @Excel(name = "智能体描述")
    @Size(min = 0, max = 500, message = "智能体描述长度不能超过500个字符")
    private String description;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private Long createUserId;

    /** 创建用户名 */
    @Excel(name = "创建用户名")
    private String createUserName;

    /** 状态(0-正常,1-停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 系统提示词 */
    @Excel(name = "系统提示词")
    @Size(min = 0, max = 2000, message = "系统提示词长度不能超过2000个字符")
    private String systemPrompt;

    /** 上下文长度 */
    @Excel(name = "上下文长度")
    private Integer contextLength;

    /** 流式输出(0-否,1-是) */
    @Excel(name = "流式输出", readConverterExp = "0=否,1=是")
    private String streamOutput;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setApiKey(String apiKey) 
    {
        this.apiKey = apiKey;
    }

    public String getApiKey() 
    {
        return apiKey;
    }

    public void setModel(String model) 
    {
        this.model = model;
    }

    public String getModel() 
    {
        return model;
    }

    public void setTemperature(Double temperature) 
    {
        this.temperature = temperature;
    }

    public Double getTemperature() 
    {
        return temperature;
    }

    public void setMaxTokens(Integer maxTokens) 
    {
        this.maxTokens = maxTokens;
    }

    public Integer getMaxTokens() 
    {
        return maxTokens;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setCreateUserId(Long createUserId) 
    {
        this.createUserId = createUserId;
    }

    public Long getCreateUserId() 
    {
        return createUserId;
    }

    public void setCreateUserName(String createUserName) 
    {
        this.createUserName = createUserName;
    }

    public String getCreateUserName() 
    {
        return createUserName;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setSystemPrompt(String systemPrompt) 
    {
        this.systemPrompt = systemPrompt;
    }

    public String getSystemPrompt() 
    {
        return systemPrompt;
    }

    public void setContextLength(Integer contextLength) 
    {
        this.contextLength = contextLength;
    }

    public Integer getContextLength() 
    {
        return contextLength;
    }

    public void setStreamOutput(String streamOutput) 
    {
        this.streamOutput = streamOutput;
    }

    public String getStreamOutput() 
    {
        return streamOutput;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("apiKey", getApiKey())
            .append("model", getModel())
            .append("temperature", getTemperature())
            .append("maxTokens", getMaxTokens())
            .append("description", getDescription())
            .append("createUserId", getCreateUserId())
            .append("createUserName", getCreateUserName())
            .append("status", getStatus())
            .append("systemPrompt", getSystemPrompt())
            .append("contextLength", getContextLength())
            .append("streamOutput", getStreamOutput())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
