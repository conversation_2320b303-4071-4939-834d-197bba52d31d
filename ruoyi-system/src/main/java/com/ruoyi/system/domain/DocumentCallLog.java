package com.ruoyi.system.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文档调用日志对象 document_call_log
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class DocumentCallLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long id;

    /** 文档ID */
    @Excel(name = "文档ID")
    private Long documentId;

    /** 文档名称 */
    @Excel(name = "文档名称")
    private String documentName;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    private String knowledgeBaseName;

    /** 调用用户ID */
    @Excel(name = "调用用户ID")
    private Long callUserId;

    /** 调用用户名 */
    @Excel(name = "调用用户名")
    private String callUserName;

    /** 调用时间 */
    @Excel(name = "调用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;

    /** 调用方式(api-接口调用,web-网页调用) */
    @Excel(name = "调用方式", readConverterExp = "api=接口调用,web=网页调用")
    private String callType;

    /** 调用结果(success-成功,failure-失败) */
    @Excel(name = "调用结果", readConverterExp = "success=成功,failure=失败")
    private String callResult;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 调用IP */
    @Excel(name = "调用IP")
    private String callIp;

    /** 响应时间(毫秒) */
    @Excel(name = "响应时间")
    private Long responseTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDocumentId(Long documentId) 
    {
        this.documentId = documentId;
    }

    public Long getDocumentId() 
    {
        return documentId;
    }

    public void setDocumentName(String documentName) 
    {
        this.documentName = documentName;
    }

    public String getDocumentName() 
    {
        return documentName;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseName(String knowledgeBaseName) 
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName() 
    {
        return knowledgeBaseName;
    }

    public void setCallUserId(Long callUserId) 
    {
        this.callUserId = callUserId;
    }

    public Long getCallUserId() 
    {
        return callUserId;
    }

    public void setCallUserName(String callUserName) 
    {
        this.callUserName = callUserName;
    }

    public String getCallUserName() 
    {
        return callUserName;
    }

    public void setCallTime(Date callTime) 
    {
        this.callTime = callTime;
    }

    public Date getCallTime() 
    {
        return callTime;
    }

    public void setCallType(String callType) 
    {
        this.callType = callType;
    }

    public String getCallType() 
    {
        return callType;
    }

    public void setCallResult(String callResult) 
    {
        this.callResult = callResult;
    }

    public String getCallResult() 
    {
        return callResult;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setCallIp(String callIp) 
    {
        this.callIp = callIp;
    }

    public String getCallIp() 
    {
        return callIp;
    }

    public void setResponseTime(Long responseTime) 
    {
        this.responseTime = responseTime;
    }

    public Long getResponseTime() 
    {
        return responseTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("documentId", getDocumentId())
            .append("documentName", getDocumentName())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("callUserId", getCallUserId())
            .append("callUserName", getCallUserName())
            .append("callTime", getCallTime())
            .append("callType", getCallType())
            .append("callResult", getCallResult())
            .append("errorMessage", getErrorMessage())
            .append("callIp", getCallIp())
            .append("responseTime", getResponseTime())
            .toString();
    }
}
