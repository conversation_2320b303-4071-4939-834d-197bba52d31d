package com.ruoyi.system.domain;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库对象 knowledge_base
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class KnowledgeBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 知识库ID */
    private Long id;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    @NotBlank(message = "知识库名称不能为空")
    @Size(min = 0, max = 30, message = "知识库名称长度不能超过30个字符")
    private String name;

    /** 知识库类型(public-公共库,private-私有库) */
    @Excel(name = "知识库类型", readConverterExp = "public=公共库,private=私有库")
    @NotBlank(message = "知识库类型不能为空")
    private String type;

    /** 知识库描述 */
    @Excel(name = "知识库描述")
    @Size(min = 0, max = 500, message = "知识库描述长度不能超过500个字符")
    private String description;

    /** 创建用户ID */
    @Excel(name = "创建用户ID")
    private Long createUserId;

    /** 创建用户名 */
    @Excel(name = "创建用户名")
    private String createUserName;

    /** 所属机构ID */
    @Excel(name = "所属机构ID")
    private Long orgId;

    /** 所属机构名称 */
    @Excel(name = "所属机构名称")
    private String orgName;

    /** 状态(0-正常,1-停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 文档总数 */
    @Excel(name = "文档总数")
    private Long documentCount;

    /** 最后更新时间 */
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setCreateUserId(Long createUserId) 
    {
        this.createUserId = createUserId;
    }

    public Long getCreateUserId() 
    {
        return createUserId;
    }

    public void setCreateUserName(String createUserName) 
    {
        this.createUserName = createUserName;
    }

    public String getCreateUserName() 
    {
        return createUserName;
    }

    public void setOrgId(Long orgId) 
    {
        this.orgId = orgId;
    }

    public Long getOrgId() 
    {
        return orgId;
    }

    public void setOrgName(String orgName) 
    {
        this.orgName = orgName;
    }

    public String getOrgName() 
    {
        return orgName;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDocumentCount(Long documentCount) 
    {
        this.documentCount = documentCount;
    }

    public Long getDocumentCount() 
    {
        return documentCount;
    }

    public void setLastUpdateTime(Date lastUpdateTime) 
    {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getLastUpdateTime() 
    {
        return lastUpdateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("type", getType())
            .append("description", getDescription())
            .append("createUserId", getCreateUserId())
            .append("createUserName", getCreateUserName())
            .append("orgId", getOrgId())
            .append("orgName", getOrgName())
            .append("status", getStatus())
            .append("documentCount", getDocumentCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("lastUpdateTime", getLastUpdateTime())
            .toString();
    }
}
