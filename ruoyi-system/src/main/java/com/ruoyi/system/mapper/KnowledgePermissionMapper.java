package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.KnowledgePermission;

/**
 * 知识库权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface KnowledgePermissionMapper
{
    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    public KnowledgePermission selectKnowledgePermissionById(Long id);

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgePermission 知识库权限
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionList(KnowledgePermission knowledgePermission);

    /**
     * 新增知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    public int insertKnowledgePermission(KnowledgePermission knowledgePermission);

    /**
     * 修改知识库权限
     * 
     * @param knowledgePermission 知识库权限
     * @return 结果
     */
    public int updateKnowledgePermission(KnowledgePermission knowledgePermission);

    /**
     * 删除知识库权限
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    public int deleteKnowledgePermissionById(Long id);

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgePermissionByIds(Long[] ids);

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionByBaseId(Long knowledgeBaseId);

    /**
     * 根据知识库ID和权限类型查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @param permissionType 权限类型
     * @return 知识库权限集合
     */
    public List<KnowledgePermission> selectKnowledgePermissionByBaseIdAndType(Long knowledgeBaseId, String permissionType);

    /**
     * 批量新增知识库权限
     * 
     * @param knowledgePermissions 知识库权限集合
     * @return 结果
     */
    public int batchInsertKnowledgePermission(List<KnowledgePermission> knowledgePermissions);

    /**
     * 根据知识库ID和权限类型删除权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param permissionType 权限类型
     * @return 结果
     */
    public int deleteKnowledgePermissionByBaseIdAndType(Long knowledgeBaseId, String permissionType);
}
