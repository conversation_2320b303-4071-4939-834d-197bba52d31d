package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AgentPermission;

/**
 * 智能体权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface AgentPermissionMapper
{
    /**
     * 查询智能体权限
     * 
     * @param id 智能体权限主键
     * @return 智能体权限
     */
    public AgentPermission selectAgentPermissionById(Long id);

    /**
     * 查询智能体权限列表
     * 
     * @param agentPermission 智能体权限
     * @return 智能体权限集合
     */
    public List<AgentPermission> selectAgentPermissionList(AgentPermission agentPermission);

    /**
     * 新增智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    public int insertAgentPermission(AgentPermission agentPermission);

    /**
     * 修改智能体权限
     * 
     * @param agentPermission 智能体权限
     * @return 结果
     */
    public int updateAgentPermission(AgentPermission agentPermission);

    /**
     * 删除智能体权限
     * 
     * @param id 智能体权限主键
     * @return 结果
     */
    public int deleteAgentPermissionById(Long id);

    /**
     * 批量删除智能体权限
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAgentPermissionByIds(Long[] ids);

    /**
     * 根据智能体ID查询权限列表
     * 
     * @param agentId 智能体ID
     * @return 智能体权限集合
     */
    public List<AgentPermission> selectAgentPermissionByAgentId(Long agentId);

    /**
     * 根据智能体ID和权限类型查询权限列表
     * 
     * @param agentId 智能体ID
     * @param permissionType 权限类型
     * @return 智能体权限集合
     */
    public List<AgentPermission> selectAgentPermissionByAgentIdAndType(Long agentId, String permissionType);

    /**
     * 批量新增智能体权限
     * 
     * @param agentPermissions 智能体权限集合
     * @return 结果
     */
    public int batchInsertAgentPermission(List<AgentPermission> agentPermissions);

    /**
     * 根据智能体ID和权限类型删除权限
     * 
     * @param agentId 智能体ID
     * @param permissionType 权限类型
     * @return 结果
     */
    public int deleteAgentPermissionByAgentIdAndType(Long agentId, String permissionType);
}
