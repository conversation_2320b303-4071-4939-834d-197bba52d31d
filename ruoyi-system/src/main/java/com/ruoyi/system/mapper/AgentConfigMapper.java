package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AgentConfig;

/**
 * 智能体配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface AgentConfigMapper
{
    /**
     * 查询智能体配置
     * 
     * @param id 智能体配置主键
     * @return 智能体配置
     */
    public AgentConfig selectAgentConfigById(Long id);

    /**
     * 查询智能体配置列表
     * 
     * @param agentConfig 智能体配置
     * @return 智能体配置集合
     */
    public List<AgentConfig> selectAgentConfigList(AgentConfig agentConfig);

    /**
     * 新增智能体配置
     * 
     * @param agentConfig 智能体配置
     * @return 结果
     */
    public int insertAgentConfig(AgentConfig agentConfig);

    /**
     * 修改智能体配置
     * 
     * @param agentConfig 智能体配置
     * @return 结果
     */
    public int updateAgentConfig(AgentConfig agentConfig);

    /**
     * 删除智能体配置
     * 
     * @param id 智能体配置主键
     * @return 结果
     */
    public int deleteAgentConfigById(Long id);

    /**
     * 批量删除智能体配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAgentConfigByIds(Long[] ids);

    /**
     * 校验智能体名称是否唯一
     * 
     * @param agentConfig 智能体配置信息
     * @return 智能体配置信息
     */
    public AgentConfig checkAgentConfigNameUnique(AgentConfig agentConfig);
}
