package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.KnowledgeDocument;

/**
 * 知识库文档Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface KnowledgeDocumentMapper
{
    /**
     * 查询知识库文档
     * 
     * @param id 知识库文档主键
     * @return 知识库文档
     */
    public KnowledgeDocument selectKnowledgeDocumentById(Long id);

    /**
     * 查询知识库文档列表
     * 
     * @param knowledgeDocument 知识库文档
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentList(KnowledgeDocument knowledgeDocument);

    /**
     * 新增知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int insertKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 修改知识库文档
     * 
     * @param knowledgeDocument 知识库文档
     * @return 结果
     */
    public int updateKnowledgeDocument(KnowledgeDocument knowledgeDocument);

    /**
     * 删除知识库文档
     * 
     * @param id 知识库文档主键
     * @return 结果
     */
    public int deleteKnowledgeDocumentById(Long id);

    /**
     * 批量删除知识库文档
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeDocumentByIds(Long[] ids);

    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库文档集合
     */
    public List<KnowledgeDocument> selectKnowledgeDocumentByKnowledgeBaseId(Long knowledgeBaseId);
}
