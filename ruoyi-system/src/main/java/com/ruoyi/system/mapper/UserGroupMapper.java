package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserGroup;
import com.ruoyi.system.domain.UserGroupMember;

/**
 * 用户组Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface UserGroupMapper
{
    /**
     * 查询用户组
     * 
     * @param id 用户组主键
     * @return 用户组
     */
    public UserGroup selectUserGroupById(Long id);

    /**
     * 查询用户组列表
     * 
     * @param userGroup 用户组
     * @return 用户组集合
     */
    public List<UserGroup> selectUserGroupList(UserGroup userGroup);

    /**
     * 新增用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    public int insertUserGroup(UserGroup userGroup);

    /**
     * 修改用户组
     * 
     * @param userGroup 用户组
     * @return 结果
     */
    public int updateUserGroup(UserGroup userGroup);

    /**
     * 删除用户组
     * 
     * @param id 用户组主键
     * @return 结果
     */
    public int deleteUserGroupById(Long id);

    /**
     * 批量删除用户组
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserGroupByIds(Long[] ids);

    /**
     * 校验用户组名称是否唯一
     * 
     * @param name 用户组名称
     * @return 用户组信息
     */
    public UserGroup checkUserGroupNameUnique(String name);
    
    /**
     * 删除用户组的所有成员
     * 
     * @param groupId 用户组ID
     * @return 结果
     */
    public int deleteUserGroupMembersByGroupId(Long groupId);
    
    /**
     * 批量插入用户组成员
     * 
     * @param members 成员列表
     * @return 结果
     */
    public int insertUserGroupMembers(List<UserGroupMember> members);
    
    /**
     * 根据用户组ID查询成员列表
     * 
     * @param groupId 用户组ID
     * @return 成员列表
     */
    public List<UserGroupMember> selectUserGroupMembersByGroupId(Long groupId);
}
