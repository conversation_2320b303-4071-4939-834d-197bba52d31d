package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserGroupMember;

/**
 * 用户组成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface UserGroupMemberMapper
{
    /**
     * 查询用户组成员
     * 
     * @param id 用户组成员主键
     * @return 用户组成员
     */
    public UserGroupMember selectUserGroupMemberById(Long id);

    /**
     * 查询用户组成员列表
     * 
     * @param userGroupMember 用户组成员
     * @return 用户组成员集合
     */
    public List<UserGroupMember> selectUserGroupMemberList(UserGroupMember userGroupMember);

    /**
     * 新增用户组成员
     * 
     * @param userGroupMember 用户组成员
     * @return 结果
     */
    public int insertUserGroupMember(UserGroupMember userGroupMember);

    /**
     * 修改用户组成员
     * 
     * @param userGroupMember 用户组成员
     * @return 结果
     */
    public int updateUserGroupMember(UserGroupMember userGroupMember);

    /**
     * 删除用户组成员
     * 
     * @param id 用户组成员主键
     * @return 结果
     */
    public int deleteUserGroupMemberById(Long id);

    /**
     * 批量删除用户组成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserGroupMemberByIds(Long[] ids);

    /**
     * 根据用户组ID查询成员列表
     * 
     * @param groupId 用户组ID
     * @return 用户组成员集合
     */
    public List<UserGroupMember> selectUserGroupMemberByGroupId(Long groupId);

    /**
     * 根据用户组ID删除成员
     * 
     * @param groupId 用户组ID
     * @return 结果
     */
    public int deleteUserGroupMemberByGroupId(Long groupId);

    /**
     * 批量新增用户组成员
     * 
     * @param userGroupMembers 用户组成员集合
     * @return 结果
     */
    public int batchInsertUserGroupMember(List<UserGroupMember> userGroupMembers);

    /**
     * 根据用户组ID和用户ID查询成员
     * 
     * @param groupId 用户组ID
     * @param userId 用户ID
     * @return 用户组成员
     */
    public UserGroupMember selectUserGroupMemberByGroupIdAndUserId(Long groupId, Long userId);
}
