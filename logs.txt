09:04:22,199 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.xml]
09:04:22,199 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback.xml] at [file:/Users/<USER>/workspace/%e4%ba%91%e5%9e%9a/RuoYi-Vue-master/ruoyi-admin/target/classes/logback.xml]
09:04:22,227 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set
09:04:22,229 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
09:04:22,230 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [console]
09:04:22,232 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
09:04:22,255 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
09:04:22,256 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [file_info]
09:04:22,261 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@641853239 - No compression will be used
09:04:22,261 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@641853239 - Will use the pattern /home/<USER>/logs/sys-info.%d{yyyy-MM-dd}.log for the active file
09:04:22,262 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd' from file name pattern '/home/<USER>/logs/sys-info.%d{yyyy-MM-dd}.log'.
09:04:22,262 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at midnight.
09:04:22,280 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to Fri Jul 18 09:04:22 CST 2025
09:04:22,280 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
09:04:22,283 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - Active log file name: /home/<USER>/logs/sys-info.log
09:04:22,283 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - File property is set to [/home/<USER>/logs/sys-info.log]
09:04:22,389 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - Failed to create parent directories for [/home/<USER>/logs/sys-info.log]
09:04:22,398 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - openFile(/home/<USER>/logs/sys-info.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-info.log (No such file or directory)
	at java.io.FileNotFoundException: /home/<USER>/logs/sys-info.log (No such file or directory)
	at 	at java.base/java.io.FileOutputStream.open0(Native Method)
	at 	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:255)
	at 	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:210)
	at 	at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
	at 	at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
	at 	at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
	at 	at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
	at 	at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
	at 	at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
	at 	at ch.qos.logback.classic.util.ContextInitializer.configureByResource(ContextInitializer.java:64)
	at 	at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:134)
	at 	at org.slf4j.impl.StaticLoggerBinder.init(StaticLoggerBinder.java:84)
	at 	at org.slf4j.impl.StaticLoggerBinder.<clinit>(StaticLoggerBinder.java:55)
	at 	at org.slf4j.LoggerFactory.bind(LoggerFactory.java:150)
	at 	at org.slf4j.LoggerFactory.performInitialization(LoggerFactory.java:124)
	at 	at org.slf4j.LoggerFactory.getILoggerFactory(LoggerFactory.java:417)
	at 	at org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:362)
	at 	at org.apache.commons.logging.LogAdapter$Slf4jAdapter.createLocationAwareLog(LogAdapter.java:130)
	at 	at org.apache.commons.logging.LogAdapter.createLog(LogAdapter.java:91)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:67)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:59)
	at 	at org.springframework.boot.SpringApplication.<clinit>(SpringApplication.java:206)
	at 	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:18)
09:04:22,398 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
09:04:22,398 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [file_error]
09:04:22,399 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@265119009 - No compression will be used
09:04:22,399 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@265119009 - Will use the pattern /home/<USER>/logs/sys-error.%d{yyyy-MM-dd}.log for the active file
09:04:22,399 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd' from file name pattern '/home/<USER>/logs/sys-error.%d{yyyy-MM-dd}.log'.
09:04:22,399 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at midnight.
09:04:22,410 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to Fri Jul 18 09:04:22 CST 2025
09:04:22,411 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
09:04:22,411 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - Active log file name: /home/<USER>/logs/sys-error.log
09:04:22,411 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - File property is set to [/home/<USER>/logs/sys-error.log]
09:04:22,490 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - Failed to create parent directories for [/home/<USER>/logs/sys-error.log]
09:04:22,498 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - openFile(/home/<USER>/logs/sys-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-error.log (No such file or directory)
	at java.io.FileNotFoundException: /home/<USER>/logs/sys-error.log (No such file or directory)
	at 	at java.base/java.io.FileOutputStream.open0(Native Method)
	at 	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:255)
	at 	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:210)
	at 	at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
	at 	at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
	at 	at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
	at 	at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
	at 	at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
	at 	at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
	at 	at ch.qos.logback.classic.util.ContextInitializer.configureByResource(ContextInitializer.java:64)
	at 	at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:134)
	at 	at org.slf4j.impl.StaticLoggerBinder.init(StaticLoggerBinder.java:84)
	at 	at org.slf4j.impl.StaticLoggerBinder.<clinit>(StaticLoggerBinder.java:55)
	at 	at org.slf4j.LoggerFactory.bind(LoggerFactory.java:150)
	at 	at org.slf4j.LoggerFactory.performInitialization(LoggerFactory.java:124)
	at 	at org.slf4j.LoggerFactory.getILoggerFactory(LoggerFactory.java:417)
	at 	at org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:362)
	at 	at org.apache.commons.logging.LogAdapter$Slf4jAdapter.createLocationAwareLog(LogAdapter.java:130)
	at 	at org.apache.commons.logging.LogAdapter.createLog(LogAdapter.java:91)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:67)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:59)
	at 	at org.springframework.boot.SpringApplication.<clinit>(SpringApplication.java:206)
	at 	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:18)
09:04:22,498 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
09:04:22,498 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [sys-user]
09:04:22,499 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@668210649 - No compression will be used
09:04:22,499 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@668210649 - Will use the pattern /home/<USER>/logs/sys-user.%d{yyyy-MM-dd}.log for the active file
09:04:22,499 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd' from file name pattern '/home/<USER>/logs/sys-user.%d{yyyy-MM-dd}.log'.
09:04:22,499 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at midnight.
09:04:22,508 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to Fri Jul 18 09:04:22 CST 2025
09:04:22,508 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
09:04:22,509 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - Active log file name: /home/<USER>/logs/sys-user.log
09:04:22,509 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - File property is set to [/home/<USER>/logs/sys-user.log]
09:04:22,581 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - Failed to create parent directories for [/home/<USER>/logs/sys-user.log]
09:04:22,587 |-ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - openFile(/home/<USER>/logs/sys-user.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-user.log (No such file or directory)
	at java.io.FileNotFoundException: /home/<USER>/logs/sys-user.log (No such file or directory)
	at 	at java.base/java.io.FileOutputStream.open0(Native Method)
	at 	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:255)
	at 	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:210)
	at 	at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
	at 	at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
	at 	at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
	at 	at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
	at 	at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
	at 	at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
	at 	at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
	at 	at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
	at 	at ch.qos.logback.classic.util.ContextInitializer.configureByResource(ContextInitializer.java:64)
	at 	at ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:134)
	at 	at org.slf4j.impl.StaticLoggerBinder.init(StaticLoggerBinder.java:84)
	at 	at org.slf4j.impl.StaticLoggerBinder.<clinit>(StaticLoggerBinder.java:55)
	at 	at org.slf4j.LoggerFactory.bind(LoggerFactory.java:150)
	at 	at org.slf4j.LoggerFactory.performInitialization(LoggerFactory.java:124)
	at 	at org.slf4j.LoggerFactory.getILoggerFactory(LoggerFactory.java:417)
	at 	at org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:362)
	at 	at org.apache.commons.logging.LogAdapter$Slf4jAdapter.createLocationAwareLog(LogAdapter.java:130)
	at 	at org.apache.commons.logging.LogAdapter.createLog(LogAdapter.java:91)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:67)
	at 	at org.apache.commons.logging.LogFactory.getLog(LogFactory.java:59)
	at 	at org.springframework.boot.SpringApplication.<clinit>(SpringApplication.java:206)
	at 	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:18)
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ruoyi] to INFO
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [org.springframework] to WARN
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to INFO
09:04:22,588 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [console] to Logger[ROOT]
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to INFO
09:04:22,588 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [file_info] to Logger[ROOT]
09:04:22,588 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [file_error] to Logger[ROOT]
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [sys-user] to INFO
09:04:22,588 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [sys-user] to Logger[sys-user]
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.
09:04:22,588 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@5c18298f - Registering current configuration as safe fallback point

09:04:23.083 [restartedMain] ERROR o.s.b.SpringApplication - [reportFailure,870] - Application run failed
java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - Failed to create parent directories for [/home/<USER>/logs/sys-info.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - openFile(/home/<USER>/logs/sys-info.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-info.log (No such file or directory)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - Failed to create parent directories for [/home/<USER>/logs/sys-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - openFile(/home/<USER>/logs/sys-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-error.log (No such file or directory)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - Failed to create parent directories for [/home/<USER>/logs/sys-user.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - openFile(/home/<USER>/logs/sys-user.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-user.log (No such file or directory)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:328)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:282)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:240)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:216)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133)
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:82)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:63)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:117)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:111)
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:62)
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:379)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:337)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:18)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - Failed to create parent directories for [/home/<USER>/logs/sys-info.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_info] - openFile(/home/<USER>/logs/sys-info.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-info.log (No such file or directory)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - Failed to create parent directories for [/home/<USER>/logs/sys-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[file_error] - openFile(/home/<USER>/logs/sys-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-error.log (No such file or directory)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - Failed to create parent directories for [/home/<USER>/logs/sys-user.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[sys-user] - openFile(/home/<USER>/logs/sys-user.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/sys-user.log (No such file or directory)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:179)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.reinitialize(LogbackLoggingSystem.java:232)
	at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:73)
	at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:60)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:132)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:313)
	... 21 common frames omitted
