-- ===========================
-- 云垚人工智能助手 - 知识库相关表结构
-- 创建时间：2025-07-30
-- 说明：仅包含表结构创建语句，不包含示例数据
-- ===========================

-- ----------------------------
-- 1. 知识库表
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_base`;
CREATE TABLE `knowledge_base` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '知识库ID',
  `name` varchar(30) NOT NULL COMMENT '知识库名称',
  `type` varchar(20) NOT NULL COMMENT '知识库类型(public-公共库,private-私有库)',
  `description` varchar(500) DEFAULT NULL COMMENT '知识库描述',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建用户名',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `org_name` varchar(50) DEFAULT NULL COMMENT '所属机构名称',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `document_count` bigint(20) DEFAULT '0' COMMENT '文档总数',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_base_name` (`name`),
  KEY `idx_knowledge_base_create_user_id` (`create_user_id`),
  KEY `idx_knowledge_base_org_id` (`org_id`),
  KEY `idx_knowledge_base_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库表';

-- ----------------------------
-- 2. 知识库文档表
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_document`;
CREATE TABLE `knowledge_document` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `knowledge_base_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `file_name` varchar(200) NOT NULL COMMENT '文档名称',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文档类型',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `status` char(1) DEFAULT '0' COMMENT '文档状态(0-未解析,1-解析中,2-解析完成,3-解析失败)',
  `parse_progress` int(3) DEFAULT '0' COMMENT '解析进度',
  `parse_result` text COMMENT '解析结果',
  `upload_user_id` bigint(20) DEFAULT NULL COMMENT '上传用户ID',
  `upload_user_name` varchar(30) DEFAULT NULL COMMENT '上传用户名',
  `download_count` bigint(20) DEFAULT '0' COMMENT '下载次数',
  `last_download_time` datetime DEFAULT NULL COMMENT '最后下载时间',
  `call_count` bigint(20) DEFAULT '0' COMMENT '调用次数',
  `last_call_time` datetime DEFAULT NULL COMMENT '最后调用时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_document_base_id` (`knowledge_base_id`),
  KEY `idx_knowledge_document_upload_user_id` (`upload_user_id`),
  KEY `idx_knowledge_document_status` (`status`),
  KEY `idx_knowledge_document_file_name` (`file_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库文档表';

-- ----------------------------
-- 3. 知识库权限表
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_permission`;
CREATE TABLE `knowledge_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `knowledge_base_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `document_id` bigint(20) DEFAULT NULL COMMENT '文档ID(为空表示知识库级别权限)',
  `permission_type` varchar(20) NOT NULL COMMENT '权限类型(org-机构,user_group-用户组)',
  `target_id` bigint(20) NOT NULL COMMENT '权限目标ID(机构ID或用户组ID)',
  `target_name` varchar(50) DEFAULT NULL COMMENT '权限目标名称',
  `permission_level` varchar(20) DEFAULT 'read' COMMENT '权限级别(read-只读,write-读写,admin-管理)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_permission_base_id` (`knowledge_base_id`),
  KEY `idx_knowledge_permission_document_id` (`document_id`),
  KEY `idx_knowledge_permission_target` (`permission_type`, `target_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库权限表';

-- ----------------------------
-- 4. 智能体配置表
-- ----------------------------
DROP TABLE IF EXISTS `agent_config`;
CREATE TABLE `agent_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '智能体ID',
  `name` varchar(50) NOT NULL COMMENT '智能体名称',
  `api_key` varchar(200) NOT NULL COMMENT 'API密钥',
  `model` varchar(100) NOT NULL COMMENT '模型名称',
  `temperature` double(3,2) DEFAULT '0.70' COMMENT '模型温度参数',
  `max_tokens` int(10) DEFAULT '1000' COMMENT '最大Token数',
  `description` varchar(500) DEFAULT NULL COMMENT '智能体描述',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建用户名',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `system_prompt` text COMMENT '系统提示词',
  `context_length` int(10) DEFAULT '4096' COMMENT '上下文长度',
  `stream_output` char(1) DEFAULT '1' COMMENT '流式输出(0-否,1-是)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_config_name` (`name`),
  KEY `idx_agent_config_create_user_id` (`create_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体配置表';

-- ----------------------------
-- 5. 智能体权限表
-- ----------------------------
DROP TABLE IF EXISTS `agent_permission`;
CREATE TABLE `agent_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `agent_id` bigint(20) NOT NULL COMMENT '智能体ID',
  `permission_type` varchar(20) NOT NULL COMMENT '权限类型(org-机构,user_group-用户组)',
  `target_id` bigint(20) NOT NULL COMMENT '权限目标ID(机构ID或用户组ID)',
  `target_name` varchar(50) DEFAULT NULL COMMENT '权限目标名称',
  `permission_level` varchar(20) DEFAULT 'read' COMMENT '权限级别(read-只读,write-读写,admin-管理)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_permission_agent_id` (`agent_id`),
  KEY `idx_agent_permission_target` (`permission_type`, `target_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体权限表';

-- ----------------------------
-- 6. 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `user_group`;
CREATE TABLE `user_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `description` varchar(500) DEFAULT NULL COMMENT '用户组描述',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `org_name` varchar(50) DEFAULT NULL COMMENT '所属机构名称',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建用户名',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `member_count` int(10) DEFAULT '0' COMMENT '成员数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_group_name_org` (`name`, `org_id`),
  KEY `idx_user_group_org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户组表';

-- ----------------------------
-- 7. 用户组成员表
-- ----------------------------
DROP TABLE IF EXISTS `user_group_member`;
CREATE TABLE `user_group_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '成员ID',
  `group_id` bigint(20) NOT NULL COMMENT '用户组ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户名',
  `nick_name` varchar(30) DEFAULT NULL COMMENT '用户昵称',
  `dept_name` varchar(50) DEFAULT NULL COMMENT '部门名称',
  `role` varchar(20) DEFAULT 'member' COMMENT '角色(member-成员,admin-管理员)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-停用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_group_member` (`group_id`, `user_id`),
  KEY `idx_user_group_member_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户组成员表';

-- ----------------------------
-- 8. 文档调用日志表
-- ----------------------------
DROP TABLE IF EXISTS `document_call_log`;
CREATE TABLE `document_call_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `document_id` bigint(20) DEFAULT NULL COMMENT '文档ID',
  `document_name` varchar(200) DEFAULT NULL COMMENT '文档名称',
  `knowledge_base_id` bigint(20) DEFAULT NULL COMMENT '知识库ID',
  `knowledge_base_name` varchar(30) DEFAULT NULL COMMENT '知识库名称',
  `call_user_id` bigint(20) DEFAULT NULL COMMENT '调用用户ID',
  `call_user_name` varchar(30) DEFAULT NULL COMMENT '调用用户名',
  `call_time` datetime DEFAULT NULL COMMENT '调用时间',
  `call_type` varchar(20) DEFAULT NULL COMMENT '调用方式(api-接口调用,web-网页调用)',
  `call_result` varchar(20) DEFAULT NULL COMMENT '调用结果(success-成功,failure-失败)',
  `error_message` text COMMENT '错误信息',
  `call_ip` varchar(50) DEFAULT NULL COMMENT '调用IP',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间(毫秒)',
  PRIMARY KEY (`id`),
  KEY `idx_document_call_log_document_id` (`document_id`),
  KEY `idx_document_call_log_base_id` (`knowledge_base_id`),
  KEY `idx_document_call_log_user_id` (`call_user_id`),
  KEY `idx_document_call_log_time` (`call_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文档调用日志表';

-- ----------------------------
-- 9. 文档下载日志表
-- ----------------------------
DROP TABLE IF EXISTS `document_download_log`;
CREATE TABLE `document_download_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `document_id` bigint(20) DEFAULT NULL COMMENT '文档ID',
  `document_name` varchar(200) DEFAULT NULL COMMENT '文档名称',
  `knowledge_base_id` bigint(20) DEFAULT NULL COMMENT '知识库ID',
  `knowledge_base_name` varchar(30) DEFAULT NULL COMMENT '知识库名称',
  `download_user_id` bigint(20) DEFAULT NULL COMMENT '下载用户ID',
  `download_user_name` varchar(30) DEFAULT NULL COMMENT '下载用户名',
  `download_time` datetime DEFAULT NULL COMMENT '下载时间',
  `download_type` varchar(20) DEFAULT NULL COMMENT '下载方式(direct-直接下载,batch-批量下载)',
  `download_result` varchar(20) DEFAULT NULL COMMENT '下载结果(success-成功,failure-失败)',
  `error_message` text COMMENT '错误信息',
  `download_ip` varchar(50) DEFAULT NULL COMMENT '下载IP',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `download_duration` bigint(20) DEFAULT NULL COMMENT '下载时长(毫秒)',
  PRIMARY KEY (`id`),
  KEY `idx_document_download_log_document_id` (`document_id`),
  KEY `idx_document_download_log_base_id` (`knowledge_base_id`),
  KEY `idx_document_download_log_user_id` (`download_user_id`),
  KEY `idx_document_download_log_time` (`download_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文档下载日志表';

-- ----------------------------
-- 10. 聊天会话表
-- ----------------------------
DROP TABLE IF EXISTS `chat_session`;
CREATE TABLE `chat_session` (
  `session_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(30) DEFAULT NULL COMMENT '用户名',
  `title` varchar(100) DEFAULT '新对话' COMMENT '会话标题',
  `status` char(1) DEFAULT '0' COMMENT '会话状态(0-正常,1-已结束)',
  `document_ids` text COMMENT '关联文档ID列表(JSON格式)',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `message_count` int(10) DEFAULT '0' COMMENT '消息数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`session_id`),
  KEY `idx_chat_session_user_id` (`user_id`),
  KEY `idx_chat_session_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='聊天会话表';

-- ----------------------------
-- 11. 聊天消息表
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message` (
  `message_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `session_id` bigint(20) NOT NULL COMMENT '会话ID',
  `message_type` varchar(20) NOT NULL COMMENT '消息类型(user-用户消息,ai-AI回复)',
  `content` text NOT NULL COMMENT '消息内容',
  `referenced_documents` text COMMENT '引用文档信息(JSON格式)',
  `status` char(1) DEFAULT '0' COMMENT '消息状态(0-正常,1-已删除)',
  `message_order` int(10) DEFAULT '0' COMMENT '消息顺序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_chat_message_session_id` (`session_id`),
  KEY `idx_chat_message_create_time` (`create_time`),
  KEY `idx_chat_message_order` (`session_id`, `message_order`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='聊天消息表';
