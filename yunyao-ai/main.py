"""
YunYao AI - RAG系统主应用
"""
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from loguru import logger

from yunyao_ai.config.settings import settings
from yunyao_ai.utils.logger import setup_logger
from yunyao_ai.api.document_api import router as document_router
from yunyao_ai.api.query_api import router as query_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("YunYao AI 正在启动...")

    # 初始化数据库连接
    try:
        from yunyao_ai.core.database import mysql_conn, qdrant_conn

        # 测试MySQL连接
        async with mysql_conn.get_async_connection() as conn:
            logger.info("MySQL连接测试成功")

        # 初始化Qdrant连接
        qdrant_client = qdrant_conn.get_client()
        logger.info("Qdrant连接初始化成功")

        # 预加载BGE-M3模型
        from yunyao_ai.services.embedding_service import embedding_service
        logger.info("BGE-M3模型预加载完成")

    except Exception as e:
        logger.error(f"初始化失败: {e}")
        raise

    logger.info("YunYao AI 启动完成")

    yield

    # 关闭时的清理
    logger.info("YunYao AI 正在关闭...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="YunYao AI - RAG系统，支持文档解析、向量检索和智能问答",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(document_router)
app.include_router(query_router)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用 YunYao AI",
        "version": settings.APP_VERSION,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        from yunyao_ai.core.database import mysql_conn
        async with mysql_conn.get_async_connection() as conn:
            pass

        return {
            "status": "healthy",
            "database": "connected",
            "version": settings.APP_VERSION
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


def main():
    """主函数"""
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )


if __name__ == "__main__":
    main()
