[project]
name = "yunyao-ai"
version = "0.1.0"
description = "YunYao AI - RAG系统，支持文档解析、向量检索和智能问答"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "qdrant-client>=1.7.0",
    "pymysql>=1.1.0",
    "cryptography>=41.0.0",
    "markitdown[all]>=0.0.1a2",
    "sentence-transformers>=2.2.2",
    "torch>=2.1.0",
    "transformers>=4.35.0",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "httpx>=0.25.0",
    "tiktoken>=0.5.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "python-docx>=1.1.0",
    "pypdf>=3.17.0",
    "openpyxl>=3.1.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "pydantic-settings>=2.10.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["yunyao_ai"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0"
]
