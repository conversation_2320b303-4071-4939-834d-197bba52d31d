"""
文本处理工具函数
"""
import re
import unicodedata
from typing import List, Optional


def clean_text(text: str) -> str:
    """
    清理文本内容
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 标准化Unicode字符
    text = unicodedata.normalize('NFKC', text)
    
    # 移除控制字符
    text = ''.join(char for char in text if unicodedata.category(char)[0] != 'C')
    
    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    text = text.strip()
    
    return text


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    提取文本关键词（简单实现）
    
    Args:
        text: 输入文本
        max_keywords: 最大关键词数量
        
    Returns:
        关键词列表
    """
    if not text:
        return []
    
    # 简单的关键词提取：去除停用词，按词频排序
    # 这里使用简单的实现，实际项目中可以使用更复杂的算法
    
    # 中文停用词（简化版）
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
        '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
        '自己', '这', '那', '里', '就是', '可以', '什么', '如果', '这个', '那个',
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being'
    }
    
    # 提取词汇（简单按空格和标点分割）
    words = re.findall(r'\b\w+\b', text.lower())
    
    # 过滤停用词和短词
    keywords = [word for word in words if len(word) > 1 and word not in stop_words]
    
    # 统计词频
    word_freq = {}
    for word in keywords:
        word_freq[word] = word_freq.get(word, 0) + 1
    
    # 按频率排序
    sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    
    return [word for word, freq in sorted_keywords[:max_keywords]]


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断文本到指定长度
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        截断后的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def split_sentences(text: str) -> List[str]:
    """
    分割句子
    
    Args:
        text: 输入文本
        
    Returns:
        句子列表
    """
    if not text:
        return []
    
    # 简单的句子分割（基于标点符号）
    sentences = re.split(r'[.!?。！？]+', text)
    
    # 清理空句子和过短的句子
    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 3]
    
    return sentences


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度（简单实现）
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        相似度分数 (0-1)
    """
    if not text1 or not text2:
        return 0.0
    
    # 提取词汇集合
    words1 = set(re.findall(r'\b\w+\b', text1.lower()))
    words2 = set(re.findall(r'\b\w+\b', text2.lower()))
    
    if not words1 or not words2:
        return 0.0
    
    # 计算Jaccard相似度
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    return intersection / union if union > 0 else 0.0


def format_context(chunks: List[dict], max_length: Optional[int] = None) -> str:
    """
    格式化上下文文本
    
    Args:
        chunks: 文档块列表
        max_length: 最大长度限制
        
    Returns:
        格式化的上下文
    """
    if not chunks:
        return ""
    
    context_parts = []
    current_length = 0
    
    for i, chunk in enumerate(chunks):
        content = chunk.get("content", "").strip()
        if not content:
            continue
        
        # 添加分隔符和序号
        formatted_chunk = f"\n--- 文档片段 {i+1} ---\n{content}\n"
        
        # 检查长度限制
        if max_length and current_length + len(formatted_chunk) > max_length:
            break
        
        context_parts.append(formatted_chunk)
        current_length += len(formatted_chunk)
    
    return "\n".join(context_parts)


def validate_query(query: str) -> tuple[bool, str]:
    """
    验证查询文本
    
    Args:
        query: 查询文本
        
    Returns:
        (是否有效, 错误消息)
    """
    if not query:
        return False, "查询不能为空"
    
    query = query.strip()
    
    if len(query) < 2:
        return False, "查询太短，至少需要2个字符"
    
    if len(query) > 1000:
        return False, "查询太长，最多1000个字符"
    
    # 检查是否包含有效字符
    if not re.search(r'[\w\u4e00-\u9fff]', query):
        return False, "查询必须包含有效的文字或数字"
    
    return True, ""
