"""
文档重排序服务
"""
import json
import re
from typing import List, Dict, Optional
import httpx
from loguru import logger

from yunyao_ai.config.settings import settings


class RerankService:
    """文档重排序服务"""
    
    def __init__(self):
        self.api_base = settings.LLM_API_BASE
        self.api_key = settings.LLM_API_KEY
        self.model_name = settings.LLM_MODEL_NAME
        self.rerank_top_k = settings.RERANK_TOP_K
    
    async def rerank_documents(self, query: str, documents: List[Dict]) -> List[Dict]:
        """
        对检索到的文档进行重排序
        
        Args:
            query: 原始查询
            documents: 检索到的文档列表
            
        Returns:
            重排序后的文档列表
        """
        try:
            if not documents:
                return []
            
            logger.info(f"开始重排序 {len(documents)} 个文档")
            
            # 如果文档数量已经小于等于目标数量，且没有配置LLM API，直接返回
            if len(documents) <= self.rerank_top_k and (not self.api_base or not self.api_key):
                logger.info("文档数量较少且未配置LLM API，跳过重排序")
                return documents[:self.rerank_top_k]
            
            # 如果没有配置LLM API，使用简单的分数排序
            if not self.api_base or not self.api_key:
                logger.warning("未配置LLM API，使用分数排序")
                return self._simple_rerank(documents)
            
            # 使用LLM进行重排序
            reranked_docs = await self._llm_rerank(query, documents)
            
            logger.info(f"文档重排序完成，返回 {len(reranked_docs)} 个文档")
            return reranked_docs
            
        except Exception as e:
            logger.error(f"文档重排序失败: {e}")
            # 失败时返回原始排序的前N个文档
            return documents[:self.rerank_top_k]
    
    def _simple_rerank(self, documents: List[Dict]) -> List[Dict]:
        """简单的基于分数的重排序"""
        try:
            # 按相似度分数排序
            sorted_docs = sorted(
                documents,
                key=lambda x: x.get("score", 0),
                reverse=True
            )
            return sorted_docs[:self.rerank_top_k]
        except Exception as e:
            logger.error(f"简单重排序失败: {e}")
            return documents[:self.rerank_top_k]
    
    async def _llm_rerank(self, query: str, documents: List[Dict]) -> List[Dict]:
        """使用LLM进行重排序"""
        try:
            # 构建重排序提示词
            prompt = self._build_rerank_prompt(query, documents)
            
            # 调用LLM API
            reranked_indices = await self._call_llm_for_rerank(prompt, len(documents))
            
            # 根据返回的索引重新排序文档
            if reranked_indices:
                reranked_docs = []
                for idx in reranked_indices[:self.rerank_top_k]:
                    if 0 <= idx < len(documents):
                        reranked_docs.append(documents[idx])
                return reranked_docs
            else:
                # LLM调用失败，使用简单排序
                return self._simple_rerank(documents)
                
        except Exception as e:
            logger.error(f"LLM重排序失败: {e}")
            return self._simple_rerank(documents)
    
    def _build_rerank_prompt(self, query: str, documents: List[Dict]) -> str:
        """构建重排序提示词"""
        # 构建文档列表
        doc_list = []
        for i, doc in enumerate(documents):
            content = doc.get("content", "")[:500]  # 限制长度
            doc_list.append(f"文档{i}: {content}")
        
        docs_text = "\n\n".join(doc_list)
        
        prompt = f"""
你是一个专业的文档相关性评估助手。给定一个用户查询和一系列文档，你需要根据文档与查询的相关性对文档进行重新排序。

用户查询：{query}

文档列表：
{docs_text}

请根据文档与查询的相关性，对文档进行重新排序。返回最相关的{self.rerank_top_k}个文档的索引（从0开始），按相关性从高到低排列。

要求：
1. 仔细分析每个文档与查询的相关性
2. 考虑语义相关性，不仅仅是关键词匹配
3. 返回格式为JSON数组，只包含文档索引数字
4. 例如：[2, 0, 5, 1, 3]

请返回重排序后的文档索引：
"""
        return prompt
    
    async def _call_llm_for_rerank(self, prompt: str, doc_count: int) -> Optional[List[int]]:
        """调用LLM进行重排序"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.1,  # 较低的温度以获得更一致的结果
                    "max_tokens": 200
                }
                
                response = await client.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    # 尝试解析JSON
                    try:
                        indices = json.loads(content)
                        if isinstance(indices, list):
                            # 验证索引的有效性
                            valid_indices = []
                            for idx in indices:
                                if isinstance(idx, int) and 0 <= idx < doc_count:
                                    if idx not in valid_indices:  # 去重
                                        valid_indices.append(idx)
                            return valid_indices
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，尝试提取数字
                        numbers = re.findall(r'\d+', content)
                        indices = []
                        for num_str in numbers:
                            idx = int(num_str)
                            if 0 <= idx < doc_count and idx not in indices:
                                indices.append(idx)
                        return indices[:self.rerank_top_k]
                    
                    return None
                else:
                    logger.error(f"LLM重排序API调用失败: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"LLM重排序API调用异常: {e}")
            return None


class MockRerankService:
    """模拟重排序服务（用于测试或无LLM API时）"""
    
    def __init__(self):
        self.rerank_top_k = settings.RERANK_TOP_K
    
    async def rerank_documents(self, query: str, documents: List[Dict]) -> List[Dict]:
        """
        模拟文档重排序
        
        Args:
            query: 原始查询
            documents: 检索到的文档列表
            
        Returns:
            重排序后的文档列表
        """
        try:
            logger.info(f"使用模拟重排序服务，处理 {len(documents)} 个文档")
            
            if not documents:
                return []
            
            # 简单的基于分数和内容长度的重排序
            def rerank_score(doc):
                base_score = doc.get("score", 0)
                content = doc.get("content", "")
                
                # 简单的相关性评分
                query_lower = query.lower()
                content_lower = content.lower()
                
                # 关键词匹配加分
                keyword_bonus = 0
                for word in query_lower.split():
                    if word in content_lower:
                        keyword_bonus += 0.1
                
                # 内容长度适中加分（太短或太长都不好）
                length_bonus = 0
                content_len = len(content)
                if 100 <= content_len <= 1000:
                    length_bonus = 0.05
                
                return base_score + keyword_bonus + length_bonus
            
            # 重新排序
            sorted_docs = sorted(
                documents,
                key=rerank_score,
                reverse=True
            )
            
            result = sorted_docs[:self.rerank_top_k]
            logger.info(f"模拟重排序完成，返回 {len(result)} 个文档")
            return result
            
        except Exception as e:
            logger.error(f"模拟重排序失败: {e}")
            return documents[:self.rerank_top_k]


# 根据配置选择服务实现
if settings.LLM_API_BASE and settings.LLM_API_KEY:
    rerank_service = RerankService()
else:
    logger.info("未配置LLM API，使用模拟重排序服务")
    rerank_service = MockRerankService()
