"""
主查询服务
整合查询扩展、向量检索、文档重排序的完整流程
"""
from typing import Dict, List, Optional
from loguru import logger

from yunyao_ai.models.document import QueryRequest, QueryResponse
from yunyao_ai.services.retrieval_service import retrieval_service
from yunyao_ai.services.rerank_service import rerank_service


class QueryService:
    """主查询服务"""
    
    def __init__(self):
        self.retrieval_service = retrieval_service
        self.rerank_service = rerank_service
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        """
        处理完整的查询流程
        
        Args:
            request: 查询请求
            
        Returns:
            查询响应
        """
        try:
            logger.info(f"开始处理查询: {request.query}")
            
            # 第一步：向量检索（包含查询扩展）
            retrieval_result = await self.retrieval_service.retrieve_documents(
                query=request.query,
                knowledge_base_id=request.knowledge_base_id,
                user_id=request.user_id,
                document_ids=request.document_ids,
                top_k=request.top_k * 2  # 检索更多文档用于重排序
            )
            
            retrieved_chunks = retrieval_result["retrieved_documents"]
            expanded_queries = retrieval_result["expanded_queries"]
            
            logger.info(f"向量检索完成，获得 {len(retrieved_chunks)} 个文档块")
            
            # 第二步：文档重排序
            reranked_chunks = await self.rerank_service.rerank_documents(
                query=request.query,
                documents=retrieved_chunks
            )
            
            logger.info(f"文档重排序完成，最终 {len(reranked_chunks)} 个文档块")
            
            # 第三步：生成上下文
            context = self._generate_context(reranked_chunks)
            
            # 构建响应
            response = QueryResponse(
                success=True,
                message="查询处理成功",
                query=request.query,
                expanded_queries=expanded_queries,
                retrieved_chunks=retrieved_chunks,
                reranked_chunks=reranked_chunks,
                context=context
            )
            
            logger.info(f"查询处理完成: {request.query}")
            return response
            
        except Exception as e:
            logger.error(f"查询处理失败: {e}")
            return QueryResponse(
                success=False,
                message=f"查询处理失败: {str(e)}",
                query=request.query,
                expanded_queries=[request.query],
                retrieved_chunks=[],
                reranked_chunks=[],
                context=""
            )
    
    def _generate_context(self, documents: List[Dict]) -> str:
        """
        从重排序后的文档生成上下文
        
        Args:
            documents: 重排序后的文档列表
            
        Returns:
            生成的上下文字符串
        """
        try:
            if not documents:
                return ""
            
            context_parts = []
            for i, doc in enumerate(documents):
                content = doc.get("content", "").strip()
                if content:
                    # 添加文档来源信息
                    doc_id = doc.get("document_id", "未知")
                    chunk_idx = doc.get("chunk_index", 0)
                    score = doc.get("score", 0)
                    
                    context_part = f"[文档{i+1} - ID:{doc_id} - 块:{chunk_idx} - 相关度:{score:.3f}]\n{content}"
                    context_parts.append(context_part)
            
            # 使用特殊分隔符连接文档
            context = "\n\n" + "="*50 + "\n\n".join(context_parts)
            
            logger.info(f"生成上下文完成，总长度: {len(context)} 字符")
            return context
            
        except Exception as e:
            logger.error(f"生成上下文失败: {e}")
            return ""
    
    async def search_in_documents(self, 
                                query: str,
                                document_ids: List[int],
                                top_k: int = 5) -> Dict:
        """
        在指定文档中搜索
        
        Args:
            query: 查询文本
            document_ids: 文档ID列表
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        try:
            logger.info(f"在指定文档中搜索: {query}, 文档数量: {len(document_ids)}")
            
            # 在指定文档中检索
            retrieved_chunks = await self.retrieval_service.retrieve_by_document_ids(
                query=query,
                document_ids=document_ids,
                top_k=top_k * 2
            )
            
            # 重排序
            reranked_chunks = await self.rerank_service.rerank_documents(
                query=query,
                documents=retrieved_chunks
            )
            
            # 生成上下文
            context = self._generate_context(reranked_chunks)
            
            return {
                "success": True,
                "query": query,
                "document_ids": document_ids,
                "retrieved_chunks": retrieved_chunks,
                "reranked_chunks": reranked_chunks,
                "context": context
            }
            
        except Exception as e:
            logger.error(f"指定文档搜索失败: {e}")
            return {
                "success": False,
                "query": query,
                "document_ids": document_ids,
                "retrieved_chunks": [],
                "reranked_chunks": [],
                "context": "",
                "error": str(e)
            }
    
    async def get_query_suggestions(self, partial_query: str) -> List[str]:
        """
        获取查询建议（基于历史查询或常见模式）
        
        Args:
            partial_query: 部分查询文本
            
        Returns:
            查询建议列表
        """
        try:
            # 这里可以实现基于历史查询的建议
            # 目前返回一些通用的查询模式
            suggestions = []
            
            if len(partial_query) >= 2:
                # 添加一些常见的查询模式
                patterns = [
                    f"如何{partial_query}",
                    f"什么是{partial_query}",
                    f"{partial_query}的方法",
                    f"{partial_query}的原理",
                    f"关于{partial_query}的信息"
                ]
                
                suggestions.extend([p for p in patterns if p != partial_query])
            
            return suggestions[:5]  # 返回最多5个建议
            
        except Exception as e:
            logger.error(f"获取查询建议失败: {e}")
            return []


# 全局查询服务实例
query_service = QueryService()
