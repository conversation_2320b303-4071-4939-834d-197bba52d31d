"""
向量化服务
"""
import asyncio
from typing import List, Union
import torch
from sentence_transformers import SentenceTransformer
from loguru import logger

from yunyao_ai.config.settings import settings
from yunyao_ai.models.document import DocumentChunk
from yunyao_ai.core.database import qdrant_conn


class EmbeddingService:
    """向量化服务"""
    
    def __init__(self):
        self.model = None
        self.device = settings.EMBEDDING_MODEL_DEVICE
        self._load_model()
    
    def _load_model(self):
        """加载BGE-M3模型"""
        try:
            logger.info(f"正在加载BGE-M3模型: {settings.EMBEDDING_MODEL_NAME}")
            self.model = SentenceTransformer(
                settings.EMBEDDING_MODEL_NAME,
                device=self.device
            )
            logger.info(f"BGE-M3模型加载成功，设备: {self.device}")
        except Exception as e:
            logger.error(f"BGE-M3模型加载失败: {e}")
            logger.warning("将使用模拟向量化服务")
            self.model = None
    
    def encode_text(self, text: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        对文本进行向量化编码

        Args:
            text: 单个文本或文本列表

        Returns:
            向量或向量列表
        """
        try:
            if self.model is None:
                # 模型未加载，返回模拟向量
                logger.warning("模型未加载，使用模拟向量")
                return self._generate_mock_vector(text)

            if isinstance(text, str):
                # 单个文本
                embeddings = self.model.encode([text], normalize_embeddings=True)
                return embeddings[0].tolist()
            else:
                # 文本列表
                embeddings = self.model.encode(text, normalize_embeddings=True)
                return [emb.tolist() for emb in embeddings]
        except Exception as e:
            logger.error(f"文本向量化失败: {e}")
            logger.warning("使用模拟向量")
            return self._generate_mock_vector(text)

    def _generate_mock_vector(self, text: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """生成模拟向量"""
        import random
        import hashlib

        def text_to_vector(t: str) -> List[float]:
            # 基于文本内容生成确定性的模拟向量
            hash_obj = hashlib.md5(t.encode())
            seed = int(hash_obj.hexdigest()[:8], 16)
            random.seed(seed)
            return [random.uniform(-1, 1) for _ in range(settings.QDRANT_VECTOR_SIZE)]

        if isinstance(text, str):
            return text_to_vector(text)
        else:
            return [text_to_vector(t) for t in text]
    
    async def encode_chunks(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """
        对文档块进行向量化
        
        Args:
            chunks: 文档块列表
            
        Returns:
            包含向量的文档块列表
        """
        try:
            if not chunks:
                return []
            
            logger.info(f"开始向量化 {len(chunks)} 个文档块")
            
            # 提取文本内容
            texts = [chunk.content for chunk in chunks]
            
            # 在线程池中执行向量化（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            vectors = await loop.run_in_executor(None, self.encode_text, texts)
            
            # 将向量添加到文档块中
            for i, chunk in enumerate(chunks):
                chunk.vector = vectors[i]
            
            logger.info(f"文档块向量化完成")
            return chunks
            
        except Exception as e:
            logger.error(f"文档块向量化失败: {e}")
            raise
    
    async def encode_query(self, query: str) -> List[float]:
        """
        对查询文本进行向量化
        
        Args:
            query: 查询文本
            
        Returns:
            查询向量
        """
        try:
            loop = asyncio.get_event_loop()
            vector = await loop.run_in_executor(None, self.encode_text, query)
            return vector
        except Exception as e:
            logger.error(f"查询向量化失败: {e}")
            raise


class VectorStorageService:
    """向量存储服务"""
    
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.qdrant_client = qdrant_conn.get_client()
    
    async def store_document_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """
        存储文档块到向量数据库
        
        Args:
            chunks: 文档块列表
            
        Returns:
            是否成功
        """
        try:
            if not chunks:
                return True
            
            logger.info(f"开始存储 {len(chunks)} 个文档块")
            
            # 向量化文档块
            vectorized_chunks = await self.embedding_service.encode_chunks(chunks)
            
            # 存储到Qdrant
            await qdrant_conn.insert_chunks(vectorized_chunks)
            
            logger.info(f"文档块存储完成")
            return True
            
        except Exception as e:
            logger.error(f"文档块存储失败: {e}")
            return False
    
    async def search_similar_chunks(self, query: str, top_k: int = 10, 
                                  filters: dict = None) -> List[dict]:
        """
        搜索相似的文档块
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            filters: 过滤条件
            
        Returns:
            相似文档块列表
        """
        try:
            # 向量化查询
            query_vector = await self.embedding_service.encode_query(query)
            
            # 在Qdrant中搜索
            results = await qdrant_conn.search_similar(
                query_vector=query_vector,
                top_k=top_k,
                document_filter=filters
            )
            
            logger.info(f"向量搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []


# 全局服务实例
embedding_service = EmbeddingService()
vector_storage_service = VectorStorageService()
