"""
查询扩展服务
"""
import asyncio
import json
from typing import List, Optional
import httpx
from loguru import logger

from yunyao_ai.config.settings import settings


class QueryExpansionService:
    """查询扩展服务"""
    
    def __init__(self):
        self.api_base = settings.LLM_API_BASE
        self.api_key = settings.LLM_API_KEY
        self.model_name = settings.LLM_MODEL_NAME
        self.expansion_count = settings.QUERY_EXPANSION_COUNT
    
    async def expand_query(self, original_query: str) -> List[str]:
        """
        扩展查询，生成多个不同视角的查询
        
        Args:
            original_query: 原始查询
            
        Returns:
            扩展后的查询列表（包含原始查询）
        """
        try:
            logger.info(f"开始扩展查询: {original_query}")
            
            # 如果没有配置LLM API，则返回原始查询
            if not self.api_base or not self.api_key:
                logger.warning("未配置LLM API，使用原始查询")
                return [original_query]
            
            # 构建提示词
            prompt = self._build_expansion_prompt(original_query)
            
            # 调用LLM API
            expanded_queries = await self._call_llm_api(prompt)
            
            # 确保包含原始查询
            if original_query not in expanded_queries:
                expanded_queries.insert(0, original_query)
            
            # 限制查询数量
            expanded_queries = expanded_queries[:self.expansion_count + 1]
            
            logger.info(f"查询扩展完成，生成 {len(expanded_queries)} 个查询")
            return expanded_queries
            
        except Exception as e:
            logger.error(f"查询扩展失败: {e}")
            # 失败时返回原始查询
            return [original_query]
    
    def _build_expansion_prompt(self, query: str) -> str:
        """构建查询扩展的提示词"""
        prompt = f"""
你是一个专业的查询扩展助手。给定一个用户查询，你需要生成{self.expansion_count}个不同视角的相关查询，以帮助更全面地检索相关文档。

要求：
1. 生成的查询应该从不同角度理解原始查询
2. 保持查询的核心意图不变
3. 使用不同的表达方式和关键词
4. 每个查询都应该是完整的、可理解的
5. 返回格式为JSON数组，只包含查询文本

原始查询：{query}

请生成{self.expansion_count}个扩展查询：
"""
        return prompt
    
    async def _call_llm_api(self, prompt: str) -> List[str]:
        """调用LLM API"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": 0.7,
                    "max_tokens": 500
                }
                
                response = await client.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    # 尝试解析JSON
                    try:
                        queries = json.loads(content)
                        if isinstance(queries, list):
                            return [q.strip() for q in queries if q.strip()]
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，尝试按行分割
                        lines = content.strip().split('\n')
                        queries = []
                        for line in lines:
                            line = line.strip()
                            # 移除序号和引号
                            line = line.lstrip('0123456789.- "\'').rstrip('"\'')
                            if line:
                                queries.append(line)
                        return queries[:self.expansion_count]
                    
                    return []
                else:
                    logger.error(f"LLM API调用失败: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"LLM API调用异常: {e}")
            return []


class MockQueryExpansionService:
    """模拟查询扩展服务（用于测试或无LLM API时）"""
    
    def __init__(self):
        self.expansion_count = settings.QUERY_EXPANSION_COUNT
    
    async def expand_query(self, original_query: str) -> List[str]:
        """
        模拟查询扩展
        
        Args:
            original_query: 原始查询
            
        Returns:
            扩展后的查询列表
        """
        try:
            logger.info(f"使用模拟查询扩展: {original_query}")
            
            # 简单的查询扩展策略
            expanded_queries = [original_query]
            
            # 添加一些常见的扩展模式
            if "如何" in original_query:
                expanded_queries.append(original_query.replace("如何", "怎样"))
                expanded_queries.append(f"{original_query}的方法")
            
            if "什么是" in original_query:
                topic = original_query.replace("什么是", "").strip()
                expanded_queries.append(f"{topic}的定义")
                expanded_queries.append(f"{topic}的概念")
            
            # 添加更通用的扩展
            if len(expanded_queries) < self.expansion_count + 1:
                expanded_queries.append(f"关于{original_query}的信息")
            
            if len(expanded_queries) < self.expansion_count + 1:
                expanded_queries.append(f"{original_query}相关内容")
            
            # 限制数量并去重
            unique_queries = []
            for query in expanded_queries:
                if query not in unique_queries:
                    unique_queries.append(query)
            
            result = unique_queries[:self.expansion_count + 1]
            logger.info(f"模拟查询扩展完成，生成 {len(result)} 个查询")
            return result
            
        except Exception as e:
            logger.error(f"模拟查询扩展失败: {e}")
            return [original_query]


# 根据配置选择服务实现
if settings.LLM_API_BASE and settings.LLM_API_KEY:
    query_expansion_service = QueryExpansionService()
else:
    logger.info("未配置LLM API，使用模拟查询扩展服务")
    query_expansion_service = MockQueryExpansionService()
