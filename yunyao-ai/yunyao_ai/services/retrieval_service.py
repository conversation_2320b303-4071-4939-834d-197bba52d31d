"""
向量检索服务
"""
from typing import List, Dict, Optional, Set
from collections import defaultdict
from loguru import logger

from yunyao_ai.config.settings import settings
from yunyao_ai.services.embedding_service import vector_storage_service
from yunyao_ai.services.query_expansion import query_expansion_service


class RetrievalService:
    """向量检索服务"""
    
    def __init__(self):
        self.vector_storage = vector_storage_service
        self.query_expansion = query_expansion_service
    
    async def retrieve_documents(self,
                               query: str,
                               knowledge_base_id: Optional[int] = None,
                               user_id: Optional[int] = None,
                               document_ids: Optional[List[int]] = None,
                               top_k: int = None) -> Dict:
        """
        检索相关文档

        Args:
            query: 原始查询
            knowledge_base_id: 知识库ID过滤
            user_id: 用户ID过滤
            document_ids: 指定搜索的文档ID列表
            top_k: 返回结果数量

        Returns:
            检索结果字典
        """
        try:
            if top_k is None:
                top_k = settings.RETRIEVAL_TOP_K
            
            logger.info(f"开始检索文档，查询: {query}")
            
            # 第一步：查询扩展
            expanded_queries = await self.query_expansion.expand_query(query)
            logger.info(f"查询扩展完成，生成 {len(expanded_queries)} 个查询")
            
            # 第二步：构建过滤条件
            filters = self._build_filters(knowledge_base_id, user_id, document_ids)

            # 第三步：多查询检索
            all_results = []
            for expanded_query in expanded_queries:
                results = await self.vector_storage.search_similar_chunks(
                    query=expanded_query,
                    top_k=top_k,
                    filters=filters
                )
                all_results.extend(results)
            
            # 第四步：合并和去重
            merged_results = self._merge_and_deduplicate(all_results, top_k)
            
            logger.info(f"文档检索完成，返回 {len(merged_results)} 个结果")
            
            return {
                "original_query": query,
                "expanded_queries": expanded_queries,
                "retrieved_documents": merged_results,
                "total_count": len(merged_results)
            }
            
        except Exception as e:
            logger.error(f"文档检索失败: {e}")
            return {
                "original_query": query,
                "expanded_queries": [query],
                "retrieved_documents": [],
                "total_count": 0
            }
    
    def _build_filters(self, knowledge_base_id: Optional[int],
                      user_id: Optional[int],
                      document_ids: Optional[List[int]] = None) -> Optional[Dict]:
        """构建过滤条件"""
        filters = {}

        if knowledge_base_id is not None:
            # 注意：这里需要根据实际的数据结构调整
            # 如果文档块中存储了knowledge_base_id，可以直接过滤
            # 否则需要通过document_id关联查询
            filters["knowledge_base_id"] = knowledge_base_id

        if user_id is not None:
            # 同样需要根据实际数据结构调整
            filters["upload_user_id"] = user_id

        if document_ids is not None and len(document_ids) > 0:
            # 指定文档ID列表过滤
            filters["document_ids"] = document_ids

        return filters if filters else None
    
    def _merge_and_deduplicate(self, results: List[Dict], top_k: int) -> List[Dict]:
        """
        合并多个查询的结果并去重
        
        Args:
            results: 所有查询的结果列表
            top_k: 最终返回的结果数量
            
        Returns:
            合并后的结果列表
        """
        if not results:
            return []
        
        # 使用文档块ID去重，并保留最高分数
        chunk_scores = defaultdict(float)
        chunk_data = {}
        
        for result in results:
            chunk_id = result["id"]
            score = result["score"]
            
            # 保留最高分数
            if chunk_id not in chunk_scores or score > chunk_scores[chunk_id]:
                chunk_scores[chunk_id] = score
                chunk_data[chunk_id] = result
        
        # 按分数排序
        sorted_chunks = sorted(
            chunk_data.values(),
            key=lambda x: x["score"],
            reverse=True
        )
        
        # 返回top_k个结果
        return sorted_chunks[:top_k]
    
    async def retrieve_by_document_ids(self, 
                                     query: str,
                                     document_ids: List[int],
                                     top_k: int = None) -> List[Dict]:
        """
        在指定文档中检索
        
        Args:
            query: 查询文本
            document_ids: 文档ID列表
            top_k: 返回结果数量
            
        Returns:
            检索结果列表
        """
        try:
            if top_k is None:
                top_k = settings.RETRIEVAL_TOP_K
            
            logger.info(f"在指定文档中检索，文档数量: {len(document_ids)}")
            
            all_results = []
            
            # 为每个文档ID进行检索
            for doc_id in document_ids:
                filters = {"document_id": doc_id}
                results = await self.vector_storage.search_similar_chunks(
                    query=query,
                    top_k=top_k,
                    filters=filters
                )
                all_results.extend(results)
            
            # 合并和排序
            merged_results = self._merge_and_deduplicate(all_results, top_k)
            
            logger.info(f"指定文档检索完成，返回 {len(merged_results)} 个结果")
            return merged_results
            
        except Exception as e:
            logger.error(f"指定文档检索失败: {e}")
            return []
    
    async def get_document_chunks_by_ids(self, chunk_ids: List[str]) -> List[Dict]:
        """
        根据块ID获取文档块详情
        
        Args:
            chunk_ids: 文档块ID列表
            
        Returns:
            文档块详情列表
        """
        try:
            # 这里需要实现从Qdrant根据ID获取文档块的逻辑
            # 由于qdrant-client的限制，这里使用搜索的方式模拟
            logger.info(f"获取文档块详情，数量: {len(chunk_ids)}")
            
            # 实际实现中可能需要调用Qdrant的retrieve方法
            # 这里简化处理
            results = []
            for chunk_id in chunk_ids:
                # 可以通过其他方式获取块详情
                pass
            
            return results
            
        except Exception as e:
            logger.error(f"获取文档块详情失败: {e}")
            return []


# 全局检索服务实例
retrieval_service = RetrievalService()
