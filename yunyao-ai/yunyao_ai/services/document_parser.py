"""
文档解析服务
"""
import os
import re
import uuid
from typing import List, <PERSON><PERSON>
from pathlib import Path
import tiktoken
from markitdown import MarkItDown
from bs4 import BeautifulSoup
from loguru import logger

from yunyao_ai.config.settings import settings
from yunyao_ai.models.document import KnowledgeDocument, DocumentChunk, DocumentStatus
from yunyao_ai.core.database import mysql_conn


class DocumentParser:
    """文档解析器"""
    
    def __init__(self):
        self.markitdown = MarkItDown()
        self.tokenizer = tiktoken.get_encoding("cl100k_base")  # GPT-3.5/4使用的编码
    
    async def parse_document(self, document_id: int, force: bool = False) -> Tuple[bool, str, List[DocumentChunk]]:
        """
        解析文档

        Args:
            document_id: 文档ID
            force: 是否强制重新解析

        Returns:
            (是否成功, 消息, 文档块列表)
        """
        try:
            # 获取文档信息
            document = await mysql_conn.get_document_by_id(document_id)
            if not document:
                return False, f"文档 {document_id} 不存在", []

            # 如果不是强制解析，检查文档状态
            if not force and document.status == DocumentStatus.PARSED:
                # 检查是否已有向量数据
                from yunyao_ai.core.database import qdrant_conn
                vector_count = await qdrant_conn.get_document_vector_count(document_id)
                if vector_count > 0:
                    return False, f"文档已解析完成，已有 {vector_count} 个向量块", []

            # 更新状态为解析中
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.PARSING, 10
            )
            
            # 检查文件是否存在
            if not document.file_path or not os.path.exists(document.file_path):
                await mysql_conn.update_document_status(
                    document_id, DocumentStatus.FAILED, 0, "文件不存在"
                )
                return False, "文件不存在", []
            
            logger.info(f"开始解析文档: {document.file_name}")
            
            # 第一步：使用MarkItDown转换为Markdown
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.PARSING, 30
            )
            
            markdown_content = self._convert_to_markdown(document.file_path)
            if not markdown_content:
                await mysql_conn.update_document_status(
                    document_id, DocumentStatus.FAILED, 0, "文档转换失败"
                )
                return False, "文档转换失败", []
            logger.info(markdown_content)
            
            # 第二步：清理文本
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.PARSING, 50
            )
            
            cleaned_content = self._clean_text(markdown_content)
            
            # 第三步：文档分块
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.PARSING, 70
            )
            
            chunks = self._split_document(document_id, cleaned_content)
            
            # 更新状态为解析完成
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.PARSED, 100, 
                f"解析完成，生成 {len(chunks)} 个文档块"
            )
            
            logger.info(f"文档解析完成: {document.file_name}, 生成 {len(chunks)} 个块")
            return True, f"解析成功，生成 {len(chunks)} 个文档块", chunks
            
        except Exception as e:
            logger.error(f"文档解析失败: {e}")
            await mysql_conn.update_document_status(
                document_id, DocumentStatus.FAILED, 0, str(e)
            )
            return False, f"解析失败: {str(e)}", []
    
    def _convert_to_markdown(self, file_path: str) -> str:
        """使用MarkItDown转换文件为Markdown"""
        try:
            result = self.markitdown.convert(file_path)
            return result.text_content if result else ""
        except Exception as e:
            logger.error(f"MarkItDown转换失败: {e}")
            return ""
    
    def _clean_text(self, content: str) -> str:
        """清理文本内容"""
        if not content:
            return ""
        
        # 使用BeautifulSoup清理HTML标签
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text()
        
        # 清理特殊字符和多余空格
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符，但保留基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()\[\]{}"\'-]', '', text)
        
        # 移除多余的换行符，但保留段落结构
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    def _split_document(self, document_id: int, content: str) -> List[DocumentChunk]:
        """分割文档为块"""
        chunks = []
        
        # 第一次分块：按段落分割
        paragraphs = self._split_by_paragraphs(content)
        
        chunk_index = 0
        for paragraph in paragraphs:
            if not paragraph.strip():
                continue
            
            # 第二次分块：按Token限制分割
            token_chunks = self._split_by_tokens(paragraph)
            
            for chunk_content in token_chunks:
                if not chunk_content.strip():
                    continue
                
                token_count = len(self.tokenizer.encode(chunk_content))
                
                chunk = DocumentChunk(
                    id=str(uuid.uuid4()),
                    document_id=document_id,
                    chunk_index=chunk_index,
                    content=chunk_content.strip(),
                    token_count=token_count,
                    metadata={
                        "source": "document_parser",
                        "chunk_method": "paragraph_token"
                    }
                )
                chunks.append(chunk)
                chunk_index += 1
        
        return chunks
    
    def _split_by_paragraphs(self, content: str) -> List[str]:
        """按段落分割文本"""
        # 按双换行符分割段落
        paragraphs = content.split('\n\n')
        
        result = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前块加上新段落超过字符限制，则保存当前块
            if len(current_chunk) + len(paragraph) > settings.CHUNK_SIZE:
                if current_chunk:
                    result.append(current_chunk)
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # 添加最后一个块
        if current_chunk:
            result.append(current_chunk)
        
        return result
    
    def _split_by_tokens(self, text: str) -> List[str]:
        """按Token数量分割文本，包含重叠处理"""
        if not text.strip():
            return []
        
        tokens = self.tokenizer.encode(text)
        
        # 如果文本已经在限制内，直接返回
        if len(tokens) <= settings.MAX_TOKENS_PER_CHUNK:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(tokens):
            # 确定结束位置
            end = min(start + settings.MAX_TOKENS_PER_CHUNK, len(tokens))
            
            # 提取当前块的tokens
            chunk_tokens = tokens[start:end]
            
            # 解码为文本
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
            
            # 如果这是最后一块，退出循环
            if end >= len(tokens):
                break
            
            # 计算下一块的开始位置（考虑重叠）
            start = end - settings.CHUNK_OVERLAP
            
            # 确保不会无限循环
            if start <= 0:
                start = end
        
        return chunks


# 全局文档解析器实例
document_parser = DocumentParser()
