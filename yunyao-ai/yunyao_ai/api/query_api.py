"""
查询相关API接口
"""
from typing import List
from fastapi import APIRouter, HTTPException, Query
from loguru import logger

from yunyao_ai.models.document import QueryRequest, QueryResponse
from yunyao_ai.services.query_service import query_service

router = APIRouter(prefix="/query", tags=["智能查询"])


@router.post("/search", response_model=QueryResponse)
async def search_documents(request: QueryRequest):
    """
    智能文档搜索接口

    完整的RAG查询流程：
    1. 查询扩展
    2. 向量检索
    3. 文档重排序
    4. 上下文生成

    Args:
        request: 查询请求，支持以下参数：
            - query: 查询内容
            - knowledge_base_id: 知识库ID过滤（可选）
            - user_id: 用户ID过滤（可选）
            - document_ids: 指定搜索的文档ID列表（可选），例如 [1,2,3,4,5]
            - top_k: 返回结果数量（默认5）

    Returns:
        查询响应，包含扩展查询、检索结果、重排序结果和生成的上下文
    """
    try:
        logger.info(f"收到智能搜索请求: {request.query}")
        
        # 处理完整的查询流程
        response = await query_service.process_query(request)
        
        logger.info(f"智能搜索完成: {request.query}")
        return response
        
    except Exception as e:
        logger.error(f"智能搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"智能搜索失败: {str(e)}")


@router.post("/search-in-documents")
async def search_in_documents(
    query: str,
    document_ids: List[int],
    top_k: int = Query(default=5, ge=1, le=20)
):
    """
    在指定文档中搜索
    
    Args:
        query: 查询文本
        document_ids: 文档ID列表
        top_k: 返回结果数量
        
    Returns:
        搜索结果
    """
    try:
        logger.info(f"在指定文档中搜索: {query}, 文档数量: {len(document_ids)}")
        
        result = await query_service.search_in_documents(
            query=query,
            document_ids=document_ids,
            top_k=top_k
        )
        
        return result
        
    except Exception as e:
        logger.error(f"指定文档搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"指定文档搜索失败: {str(e)}")


@router.get("/suggestions")
async def get_query_suggestions(q: str = Query(..., min_length=1, max_length=100)):
    """
    获取查询建议
    
    Args:
        q: 部分查询文本
        
    Returns:
        查询建议列表
    """
    try:
        suggestions = await query_service.get_query_suggestions(q)
        
        return {
            "query": q,
            "suggestions": suggestions
        }
        
    except Exception as e:
        logger.error(f"获取查询建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取查询建议失败: {str(e)}")


@router.get("/expand")
async def expand_query(query: str = Query(..., min_length=1, max_length=1000)):
    """
    查询扩展接口（用于调试和测试）
    
    Args:
        query: 原始查询
        
    Returns:
        扩展后的查询列表
    """
    try:
        from yunyao_ai.services.query_expansion import query_expansion_service
        
        expanded_queries = await query_expansion_service.expand_query(query)
        
        return {
            "original_query": query,
            "expanded_queries": expanded_queries,
            "count": len(expanded_queries)
        }
        
    except Exception as e:
        logger.error(f"查询扩展失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询扩展失败: {str(e)}")


@router.post("/retrieve")
async def retrieve_documents(
    query: str,
    knowledge_base_id: int = None,
    user_id: int = None,
    top_k: int = Query(default=10, ge=1, le=50)
):
    """
    向量检索接口（用于调试和测试）
    
    Args:
        query: 查询文本
        knowledge_base_id: 知识库ID过滤
        user_id: 用户ID过滤
        top_k: 返回结果数量
        
    Returns:
        检索结果
    """
    try:
        from yunyao_ai.services.retrieval_service import retrieval_service
        
        result = await retrieval_service.retrieve_documents(
            query=query,
            knowledge_base_id=knowledge_base_id,
            user_id=user_id,
            top_k=top_k
        )
        
        return result
        
    except Exception as e:
        logger.error(f"向量检索失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量检索失败: {str(e)}")


@router.post("/rerank")
async def rerank_documents(query: str, documents: List[dict]):
    """
    文档重排序接口（用于调试和测试）
    
    Args:
        query: 查询文本
        documents: 文档列表
        
    Returns:
        重排序后的文档列表
    """
    try:
        from yunyao_ai.services.rerank_service import rerank_service
        
        reranked_docs = await rerank_service.rerank_documents(query, documents)
        
        return {
            "query": query,
            "original_count": len(documents),
            "reranked_documents": reranked_docs,
            "reranked_count": len(reranked_docs)
        }
        
    except Exception as e:
        logger.error(f"文档重排序失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档重排序失败: {str(e)}")
