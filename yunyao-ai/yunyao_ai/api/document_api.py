"""
文档相关API接口
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from loguru import logger

from yunyao_ai.models.document import ParseDocumentRequest, ParseDocumentResponse
from yunyao_ai.services.document_parser import document_parser
from yunyao_ai.services.embedding_service import vector_storage_service

router = APIRouter(prefix="/document", tags=["文档管理"])


@router.post("/parse/{document_id}", response_model=ParseDocumentResponse)
async def parse_document(document_id: int, background_tasks: BackgroundTasks, force: bool = False):
    """
    解析文档接口

    Args:
        document_id: 文档ID
        background_tasks: 后台任务
        force: 是否强制重新解析（默认False）

    Returns:
        解析结果
    """
    try:
        logger.info(f"收到文档解析请求: {document_id}, 强制重解析: {force}")

        # 检查文档是否存在
        from yunyao_ai.core.database import mysql_conn
        document = await mysql_conn.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 检查文档状态
        if not force and document.status == "2":  # 已解析完成
            return ParseDocumentResponse(
                success=True,
                message=f"文档已解析完成，如需重新解析请使用 force=true 参数",
                document_id=document_id,
                chunks_count=None
            )

        # 如果正在解析中，不允许重复解析
        if document.status == "1":  # 解析中
            return ParseDocumentResponse(
                success=False,
                message="文档正在解析中，请稍后再试",
                document_id=document_id
            )

        # 添加后台任务进行文档解析和向量化
        background_tasks.add_task(parse_and_vectorize_document, document_id, force)

        return ParseDocumentResponse(
            success=True,
            message="文档解析任务已启动，正在后台处理",
            document_id=document_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档解析请求处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档解析请求处理失败: {str(e)}")


async def parse_and_vectorize_document(document_id: int, force: bool = False):
    """
    后台任务：解析文档并进行向量化存储

    Args:
        document_id: 文档ID
        force: 是否强制重新解析
    """
    try:
        logger.info(f"开始后台处理文档: {document_id}, 强制重解析: {force}")

        # 如果是强制重新解析，先清理旧的向量数据
        if force:
            logger.info(f"强制重新解析，清理文档 {document_id} 的旧向量数据")
            # 先检查是否有向量数据
            from yunyao_ai.core.database import qdrant_conn
            vector_count = await qdrant_conn.get_document_vector_count(document_id)
            if vector_count > 0:
                logger.info(f"发现 {vector_count} 个向量点，开始删除")
                await delete_document_vectors(document_id)
                # 验证删除是否成功
                remaining_count = await qdrant_conn.get_document_vector_count(document_id)
                if remaining_count > 0:
                    logger.warning(f"删除后仍有 {remaining_count} 个向量点残留")
                else:
                    logger.info("向量数据删除完成")
            else:
                logger.info("没有发现旧的向量数据")

        # 第一步：解析文档
        success, message, chunks = await document_parser.parse_document(document_id, force)

        if not success:
            logger.error(f"文档解析失败: {message}")
            return

        # 第二步：向量化并存储
        if chunks:
            storage_success = await vector_storage_service.store_document_chunks(chunks)
            if storage_success:
                logger.info(f"文档 {document_id} 处理完成，生成 {len(chunks)} 个向量")
            else:
                logger.error(f"文档 {document_id} 向量存储失败")
        else:
            logger.warning(f"文档 {document_id} 没有生成有效的文档块")

    except Exception as e:
        logger.error(f"后台处理文档失败: {e}")


async def delete_document_vectors(document_id: int):
    """
    删除指定文档的所有向量数据

    Args:
        document_id: 文档ID
    """
    try:
        from yunyao_ai.core.database import qdrant_conn

        # 从Qdrant中删除该文档的所有向量点
        await qdrant_conn.delete_document_vectors(document_id)
        logger.info(f"已删除文档 {document_id} 的所有向量数据")

    except Exception as e:
        logger.error(f"删除文档 {document_id} 向量数据失败: {e}")


@router.get("/status/{document_id}")
async def get_document_status(document_id: int):
    """
    获取文档解析状态
    
    Args:
        document_id: 文档ID
        
    Returns:
        文档状态信息
    """
    try:
        from yunyao_ai.core.database import mysql_conn
        
        document = await mysql_conn.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return {
            "document_id": document_id,
            "status": document.status,
            "parse_progress": document.parse_progress,
            "parse_result": document.parse_result,
            "file_name": document.file_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档状态失败: {str(e)}")


@router.delete("/chunks/{document_id}")
async def delete_document_chunks(document_id: int):
    """
    删除文档的所有向量块

    Args:
        document_id: 文档ID

    Returns:
        删除结果
    """
    try:
        logger.info(f"删除文档向量块: {document_id}")

        # 检查文档是否存在
        from yunyao_ai.core.database import mysql_conn
        document = await mysql_conn.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 删除向量数据
        await delete_document_vectors(document_id)

        # 重置文档状态为未解析
        await mysql_conn.update_document_status(
            document_id, "0", 0, "向量数据已清理，可重新解析"
        )

        return {
            "success": True,
            "message": f"文档 {document_id} 的向量块已删除，状态已重置",
            "document_id": document_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档向量块失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文档向量块失败: {str(e)}")


@router.get("/info/{document_id}")
async def get_document_info(document_id: int):
    """
    获取文档详细信息
    
    Args:
        document_id: 文档ID
        
    Returns:
        文档详细信息
    """
    try:
        from yunyao_ai.core.database import mysql_conn
        
        document = await mysql_conn.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 获取向量数据统计
        from yunyao_ai.core.database import qdrant_conn
        vector_count = await qdrant_conn.get_document_vector_count(document_id)

        return {
            "id": document.id,
            "knowledge_base_id": document.knowledge_base_id,
            "file_name": document.file_name,
            "file_type": document.file_type,
            "file_size": document.file_size,
            "status": document.status,
            "parse_progress": document.parse_progress,
            "upload_user_name": document.upload_user_name,
            "create_time": document.create_time,
            "update_time": document.update_time,
            "vector_count": vector_count
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档信息失败: {str(e)}")


@router.get("/vectors/{document_id}")
async def get_document_vectors_info(document_id: int):
    """
    获取文档向量数据信息

    Args:
        document_id: 文档ID

    Returns:
        向量数据信息
    """
    try:
        from yunyao_ai.core.database import mysql_conn, qdrant_conn

        # 检查文档是否存在
        document = await mysql_conn.get_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 获取向量数据统计
        vector_count = await qdrant_conn.get_document_vector_count(document_id)

        return {
            "document_id": document_id,
            "file_name": document.file_name,
            "status": document.status,
            "vector_count": vector_count,
            "has_vectors": vector_count > 0,
            "can_search": document.status == "2" and vector_count > 0
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档向量信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档向量信息失败: {str(e)}")
