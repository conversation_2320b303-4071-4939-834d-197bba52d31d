"""
文档相关的数据模型
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class KnowledgeDocument(BaseModel):
    """知识库文档模型"""
    id: Optional[int] = None
    knowledge_base_id: int = Field(..., description="知识库ID")
    file_name: str = Field(..., max_length=200, description="文档名称")
    file_type: Optional[str] = Field(None, max_length=50, description="文档类型")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    file_path: Optional[str] = Field(None, max_length=500, description="文件路径")
    status: str = Field(default="0", max_length=1, description="文档状态(0-未解析,1-解析中,2-解析完成,3-解析失败)")
    parse_progress: int = Field(default=0, description="解析进度")
    parse_result: Optional[str] = Field(None, description="解析结果")
    upload_user_id: Optional[int] = Field(None, description="上传用户ID")
    upload_user_name: Optional[str] = Field(None, max_length=30, description="上传用户名")
    download_count: int = Field(default=0, description="下载次数")
    last_download_time: Optional[datetime] = Field(None, description="最后下载时间")
    call_count: int = Field(default=0, description="调用次数")
    last_call_time: Optional[datetime] = Field(None, description="最后调用时间")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class DocumentChunk(BaseModel):
    """文档分块模型"""
    id: Optional[str] = None  # Qdrant中的点ID
    document_id: int = Field(..., description="文档ID")
    chunk_index: int = Field(..., description="分块索引")
    content: str = Field(..., description="分块内容")
    token_count: int = Field(..., description="Token数量")
    vector: Optional[List[float]] = Field(None, description="向量数据")
    metadata: Optional[dict] = Field(default_factory=dict, description="元数据")

    class Config:
        from_attributes = True


class ParseDocumentRequest(BaseModel):
    """解析文档请求模型"""
    document_id: int = Field(..., description="文档ID")


class ParseDocumentResponse(BaseModel):
    """解析文档响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    document_id: int = Field(..., description="文档ID")
    chunks_count: Optional[int] = Field(None, description="生成的分块数量")


class QueryRequest(BaseModel):
    """查询请求模型"""
    query: str = Field(..., min_length=1, max_length=1000, description="查询内容")
    knowledge_base_id: Optional[int] = Field(None, description="知识库ID，不指定则搜索所有")
    user_id: Optional[int] = Field(None, description="用户ID，用于权限过滤")
    document_ids: Optional[List[int]] = Field(None, description="指定搜索的文档ID列表，不指定则搜索所有文档")
    top_k: Optional[int] = Field(5, ge=1, le=20, description="返回结果数量")


class QueryResponse(BaseModel):
    """查询响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    query: str = Field(..., description="原始查询")
    expanded_queries: List[str] = Field(default_factory=list, description="扩展查询")
    retrieved_chunks: List[dict] = Field(default_factory=list, description="检索到的文档块")
    reranked_chunks: List[dict] = Field(default_factory=list, description="重排序后的文档块")
    context: str = Field(default="", description="生成的上下文")


class DocumentStatus:
    """文档状态常量"""
    UNPARSED = "0"  # 未解析
    PARSING = "1"   # 解析中
    PARSED = "2"    # 解析完成
    FAILED = "3"    # 解析失败
