"""
项目配置文件
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    APP_NAME: str = "YunYao AI"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 1025
    
    # MySQL数据库配置
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = ""
    MYSQL_DATABASE: str = "ry_vue"
    
    @property
    def mysql_url(self) -> str:
        return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}"
    
    # Qdrant向量数据库配置
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: int = 6333
    QDRANT_COLLECTION_NAME: str = "yunyao_documents"
    QDRANT_VECTOR_SIZE: int = 1024  # BGE-M3模型的向量维度
    QDRANT_STORAGE_PATH: str = "./qdrant_storage"  # 本地持久化路径
    
    # BGE-M3模型配置
    EMBEDDING_MODEL_NAME: str = "BAAI/bge-m3"
    EMBEDDING_MODEL_DEVICE: str = "cpu"  # 可以设置为 "cuda" 如果有GPU
    
    # LLM配置 (用于查询扩展和重排序)
    LLM_API_BASE: Optional[str] = None  # 如果使用API服务
    LLM_API_KEY: Optional[str] = None
    LLM_MODEL_NAME: str = "gpt-3.5-turbo"  # 默认模型
    
    # 文档处理配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    CHUNK_SIZE: int = 500  # 第一次分块大小
    CHUNK_OVERLAP: int = 50  # 重叠token数
    MAX_TOKENS_PER_CHUNK: int = 512  # 每块最大token数
    
    # 检索配置
    RETRIEVAL_TOP_K: int = 10  # 向量检索返回的文档数
    RERANK_TOP_K: int = 5  # 重排序后保留的文档数
    QUERY_EXPANSION_COUNT: int = 3  # 查询扩展生成的查询数量
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/yunyao_ai.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()
