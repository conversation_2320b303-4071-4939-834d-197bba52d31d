"""
数据库连接模块
"""
import asyncio
from typing import Optional, List, Dict, Any
import pymysql
from contextlib import asynccontextmanager
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue, MatchAny, FilterSelector
from loguru import logger

from yunyao_ai.config.settings import settings
from yunyao_ai.models.document import KnowledgeDocument, DocumentChunk


class MySQLConnection:
    """MySQL数据库连接管理"""
    
    def __init__(self):
        self.connection_params = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.connection_params)
            logger.info("MySQL连接成功")
            return connection
        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            raise
    
    @asynccontextmanager
    async def get_async_connection(self):
        """异步获取数据库连接"""
        connection = None
        try:
            # 在线程池中执行数据库连接
            loop = asyncio.get_event_loop()
            connection = await loop.run_in_executor(None, self.get_connection)
            yield connection
        finally:
            if connection:
                connection.close()
    
    async def get_document_by_id(self, document_id: int) -> Optional[KnowledgeDocument]:
        """根据ID获取文档"""
        async with self.get_async_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            try:
                sql = """
                SELECT id, knowledge_base_id, file_name, file_type, file_size, 
                       file_path, status, parse_progress, parse_result,
                       upload_user_id, upload_user_name, download_count,
                       last_download_time, call_count, last_call_time,
                       create_time, update_time
                FROM knowledge_document 
                WHERE id = %s
                """
                cursor.execute(sql, (document_id,))
                result = cursor.fetchone()
                if result:
                    return KnowledgeDocument(**result)
                return None
            finally:
                cursor.close()
    
    async def update_document_status(self, document_id: int, status: str, 
                                   parse_progress: int = None, 
                                   parse_result: str = None):
        """更新文档状态"""
        async with self.get_async_connection() as conn:
            cursor = conn.cursor()
            try:
                update_fields = ["status = %s"]
                params = [status]
                
                if parse_progress is not None:
                    update_fields.append("parse_progress = %s")
                    params.append(parse_progress)
                
                if parse_result is not None:
                    update_fields.append("parse_result = %s")
                    params.append(parse_result)
                
                update_fields.append("update_time = NOW()")
                params.append(document_id)
                
                sql = f"""
                UPDATE knowledge_document 
                SET {', '.join(update_fields)}
                WHERE id = %s
                """
                cursor.execute(sql, params)
                conn.commit()
                logger.info(f"文档 {document_id} 状态更新为 {status} 进度 {parse_progress}")
            finally:
                cursor.close()


class QdrantConnection:
    """Qdrant向量数据库连接管理"""
    
    def __init__(self):
        self.client = None
        self.collection_name = settings.QDRANT_COLLECTION_NAME
    
    def get_client(self) -> QdrantClient:
        """获取Qdrant客户端"""
        if self.client is None:
            try:
                self.client = QdrantClient(
                    path=settings.QDRANT_STORAGE_PATH  # 使用本地持久化存储
                )
                logger.info(f"Qdrant客户端连接成功，存储路径: {settings.QDRANT_STORAGE_PATH}")
                self._ensure_collection_exists()
            except Exception as e:
                logger.error(f"Qdrant连接失败: {e}")
                raise
        return self.client
    
    def _ensure_collection_exists(self):
        """确保集合存在"""
        try:
            collections = self.client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=settings.QDRANT_VECTOR_SIZE,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"创建Qdrant集合: {self.collection_name}")
            else:
                logger.info(f"Qdrant集合已存在: {self.collection_name}")
        except Exception as e:
            logger.error(f"创建Qdrant集合失败: {e}")
            raise
    
    async def insert_chunks(self, chunks: List[DocumentChunk]):
        """插入文档块"""
        try:
            if not chunks:
                logger.info("没有文档块需要插入")
                return

            # 按文档ID分组，确保同一文档的向量点一起处理
            document_chunks = {}
            for chunk in chunks:
                if chunk.vector:
                    doc_id = chunk.document_id
                    if doc_id not in document_chunks:
                        document_chunks[doc_id] = []
                    document_chunks[doc_id].append(chunk)

            total_inserted = 0
            for doc_id, doc_chunks in document_chunks.items():
                logger.info(f"准备插入文档 {doc_id} 的 {len(doc_chunks)} 个向量点")

                points = []
                for chunk in doc_chunks:
                    point = PointStruct(
                        id=chunk.id,
                        vector=chunk.vector,
                        payload={
                            "document_id": chunk.document_id,
                            "chunk_index": chunk.chunk_index,
                            "content": chunk.content,
                            "token_count": chunk.token_count,
                            **chunk.metadata
                        }
                    )
                    points.append(point)

                if points:
                    self.client.upsert(
                        collection_name=self.collection_name,
                        points=points
                    )
                    total_inserted += len(points)
                    logger.info(f"成功插入文档 {doc_id} 的 {len(points)} 个向量点")

            logger.info(f"总共插入 {total_inserted} 个向量点到Qdrant")

        except Exception as e:
            logger.error(f"插入向量点失败: {e}")
            raise
    
    async def search_similar(self, query_vector: List[float], 
                           top_k: int = 10,
                           document_filter: Optional[Dict] = None) -> List[Dict]:
        """相似性搜索"""
        try:
            search_filter = None
            if document_filter:
                conditions = []
                for key, value in document_filter.items():
                    if key == "document_ids" and isinstance(value, list):
                        # 支持多个文档ID的过滤
                        conditions.append(
                            FieldCondition(key="document_id", match=MatchAny(any=value))
                        )
                    else:
                        # 单个值的过滤
                        conditions.append(
                            FieldCondition(key=key, match=MatchValue(value=value))
                        )
                if conditions:
                    search_filter = Filter(must=conditions)
            
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                query_filter=search_filter,
                limit=top_k,
                with_payload=True
            )
            
            return [
                {
                    "id": result.id,
                    "score": result.score,
                    "content": result.payload.get("content", ""),
                    "document_id": result.payload.get("document_id"),
                    "chunk_index": result.payload.get("chunk_index"),
                    "metadata": {k: v for k, v in result.payload.items() 
                               if k not in ["content", "document_id", "chunk_index"]}
                }
                for result in results
            ]
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            raise

    async def delete_document_vectors(self, document_id: int):
        """删除指定文档的所有向量点"""
        try:
            # 先检查要删除的向量点数量
            initial_count = await self.get_document_vector_count(document_id)
            if initial_count == 0:
                logger.info(f"文档 {document_id} 没有向量点需要删除")
                return

            logger.info(f"准备删除文档 {document_id} 的 {initial_count} 个向量点")

            # 构建过滤条件
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_id)
                    )
                ]
            )

            # 删除匹配的向量点
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=FilterSelector(filter=filter_condition)
            )

            # 验证删除结果
            remaining_count = await self.get_document_vector_count(document_id)
            if remaining_count == 0:
                logger.info(f"成功删除文档 {document_id} 的所有 {initial_count} 个向量点")
            else:
                logger.warning(f"删除不完整：文档 {document_id} 还剩余 {remaining_count} 个向量点")
                # 如果还有剩余，再次尝试删除
                if remaining_count > 0:
                    logger.info("尝试再次删除剩余向量点")
                    self.client.delete(
                        collection_name=self.collection_name,
                        points_selector=FilterSelector(filter=filter_condition)
                    )
                    final_count = await self.get_document_vector_count(document_id)
                    if final_count == 0:
                        logger.info(f"第二次删除成功，文档 {document_id} 的向量点已全部清理")
                    else:
                        logger.error(f"删除失败：文档 {document_id} 仍有 {final_count} 个向量点")

        except Exception as e:
            logger.error(f"删除文档 {document_id} 向量点失败: {e}")
            raise

    async def get_document_vector_count(self, document_id: int) -> int:
        """获取指定文档的向量点数量"""
        try:
            # 构建过滤条件
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_id)
                    )
                ]
            )

            # 搜索匹配的向量点（只获取数量）
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=[0.0] * settings.QDRANT_VECTOR_SIZE,  # 虚拟查询向量
                query_filter=filter_condition,
                limit=10000,  # 设置一个较大的限制
                with_payload=False,
                with_vectors=False
            )

            return len(results)

        except Exception as e:
            logger.error(f"获取文档 {document_id} 向量点数量失败: {e}")
            return 0


# 全局数据库连接实例
mysql_conn = MySQLConnection()
qdrant_conn = QdrantConnection()
