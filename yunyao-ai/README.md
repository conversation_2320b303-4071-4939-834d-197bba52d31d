# YunYao AI - RAG系统

YunYao AI 是一个基于检索增强生成（RAG）技术的智能问答系统，支持文档解析、向量检索和智能问答。

## 功能特性

### 🔍 智能文档处理
- 支持多种文档格式（PDF、Word、TXT、Excel等）
- 使用MarkItDown库进行文档转换
- 智能文本清理和预处理
- 递归分块策略，支持Token级别的精确控制

### 🧠 向量检索系统
- 基于BGE-M3模型的高质量文本向量化
- Qdrant向量数据库，支持本地持久化存储
- 高效的相似性搜索和过滤功能
- 支持多查询合并和去重

### 🚀 智能查询流程
- **查询扩展**：LLM生成多角度查询，提高召回率
- **向量检索**：基于语义相似性的文档检索
- **指定文档搜索**：支持在特定文档集合中精确搜索
- **文档重排序**：LLM评估相关性，优化结果排序
- **上下文生成**：为下游应用提供结构化上下文

### 🛠 技术架构
- FastAPI + Python 3.12
- MySQL数据库存储文档元数据
- Qdrant向量数据库存储文档向量
- BGE-M3模型进行文本向量化
- 支持LLM API集成（查询扩展和重排序）

## 快速开始

### 环境要求
- Python 3.12+
- UV包管理器
- MySQL数据库
- 足够的磁盘空间用于模型和向量存储

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd yunyao-ai
```

2. **安装依赖**
```bash
uv sync
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等参数
```

4. **启动服务**
```bash
uv run python main.py
```

服务将在 `http://localhost:8000` 启动。

### API文档
启动服务后，访问以下地址查看API文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 核心API接口

### 文档管理
- `POST /document/parse/{document_id}` - 解析文档
- `GET /document/status/{document_id}` - 获取解析状态
- `GET /document/info/{document_id}` - 获取文档信息

### 智能查询
- `POST /query/search` - 智能文档搜索（完整RAG流程）
- `POST /query/search-in-documents` - 在指定文档中搜索
- `GET /query/suggestions` - 获取查询建议

### 调试接口
- `GET /query/expand` - 查询扩展测试
- `POST /query/retrieve` - 向量检索测试
- `POST /query/rerank` - 文档重排序测试

## 配置说明

### 数据库配置
```python
# MySQL配置
MYSQL_HOST=**************
MYSQL_PORT=3307
MYSQL_USER=yunyao
MYSQL_PASSWORD=ArshBEYrGPP3nsSr
MYSQL_DATABASE=yunyao

# Qdrant配置
QDRANT_STORAGE_PATH=./qdrant_storage  # 本地持久化路径
```

### 模型配置
```python
# BGE-M3模型配置
EMBEDDING_MODEL_NAME=BAAI/bge-m3
EMBEDDING_MODEL_DEVICE=cpu  # 或 cuda

# LLM配置（可选）
LLM_API_BASE=your_llm_api_base
LLM_API_KEY=your_llm_api_key
LLM_MODEL_NAME=gpt-3.5-turbo
```

### 处理参数
```python
# 文档分块配置
CHUNK_SIZE=500              # 第一次分块大小
CHUNK_OVERLAP=50           # 重叠token数
MAX_TOKENS_PER_CHUNK=1024  # 每块最大token数

# 检索配置
RETRIEVAL_TOP_K=10         # 向量检索返回数量
RERANK_TOP_K=5            # 重排序后保留数量
QUERY_EXPANSION_COUNT=3    # 查询扩展数量
```

## 使用示例

### 1. 解析文档
```python
import httpx

# 解析文档
response = httpx.post("http://localhost:8000/document/parse/123")
print(response.json())

# 检查解析状态
response = httpx.get("http://localhost:8000/document/status/123")
print(response.json())
```

### 2. 智能查询
```python
import httpx

# 完整的RAG查询
query_data = {
    "query": "如何设计RAG系统？",
    "knowledge_base_id": 1,
    "top_k": 5
}

response = httpx.post(
    "http://localhost:8000/query/search",
    json=query_data
)

result = response.json()
print("原始查询:", result["query"])
print("扩展查询:", result["expanded_queries"])
print("最终上下文:", result["context"])
```

### 3. 指定文档搜索
```python
import httpx

# 在指定文档中搜索
query_data = {
    "query": "机器学习算法优化",
    "document_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9],  # 指定搜索的文档ID
    "top_k": 5
}

response = httpx.post(
    "http://localhost:8000/query/search",
    json=query_data
)

result = response.json()
print(f"在文档 {query_data['document_ids']} 中搜索")
print(f"找到 {len(result['reranked_chunks'])} 个相关结果")

for i, chunk in enumerate(result['reranked_chunks'], 1):
    print(f"{i}. [文档{chunk['document_id']}] {chunk['content'][:100]}...")
```

## 项目结构

```
yunyao_ai/
├── api/                 # API接口层
│   ├── document_api.py  # 文档管理接口
│   └── query_api.py     # 查询接口
├── core/                # 核心模块
│   └── database.py      # 数据库连接
├── models/              # 数据模型
│   └── document.py      # 文档相关模型
├── services/            # 业务服务层
│   ├── document_parser.py    # 文档解析服务
│   ├── embedding_service.py  # 向量化服务
│   ├── query_expansion.py    # 查询扩展服务
│   ├── retrieval_service.py  # 检索服务
│   ├── rerank_service.py     # 重排序服务
│   └── query_service.py      # 主查询服务
├── utils/               # 工具模块
│   ├── logger.py        # 日志配置
│   └── text_utils.py    # 文本处理工具
└── config/              # 配置模块
    └── settings.py      # 应用配置
```

## 开发指南

### 添加新的文档格式支持
1. 在 `document_parser.py` 中扩展 `_convert_to_markdown` 方法
2. 添加对应的文件类型检测逻辑

### 自定义向量模型
1. 修改 `embedding_service.py` 中的模型加载逻辑
2. 更新配置文件中的模型参数

### 集成新的LLM服务
1. 修改 `query_expansion.py` 和 `rerank_service.py` 中的API调用逻辑
2. 添加对应的配置参数

## 性能优化

### 向量检索优化
- 使用适当的向量维度（默认1024维）
- 合理设置检索数量和重排序数量
- 考虑使用GPU加速向量化过程

### 内存管理
- 大文档分批处理
- 及时释放不需要的向量数据
- 使用连接池管理数据库连接

## 故障排除

### 常见问题
1. **模型下载失败**：检查网络连接，考虑使用镜像源
2. **内存不足**：减少批处理大小，使用CPU而非GPU
3. **数据库连接失败**：检查连接参数和网络可达性
4. **向量搜索慢**：检查Qdrant配置和索引状态

### 日志查看
```bash
tail -f logs/yunyao_ai.log
```

## 许可证

[添加许可证信息]

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

[添加联系方式]