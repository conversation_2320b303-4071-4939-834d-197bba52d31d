// 知识库权限管理工具类
export const PermissionTreeHelper = {
  /**
   * 处理树节点数据，保持知识库节点可选择
   * @param {Array} treeData - 树数据
   * @returns {Array} 处理后的树数据
   */
  processTreeData(treeData) {
    return treeData.map(node => {
      const processedNode = { ...node };

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        processedNode.children = this.processTreeData(node.children);
      }

      return processedNode;
    });
  },

  /**
   * 从选中的节点中过滤出文档ID（忽略知识库节点选择）
   * @param {Array} selectedKeys - 选中的节点key数组
   * @param {Array} treeData - 完整的树数据，用于获取知识库下的文档
   * @returns {Array} 文档ID数组
   */
  filterDocumentIds(selectedKeys, treeData = []) {
    if (!selectedKeys || !Array.isArray(selectedKeys)) {
      return [];
    }

    const documentIds = new Set();

    selectedKeys.forEach(key => {
      const keyStr = key.toString();

      if (keyStr.startsWith('kb_')) {
        // 知识库节点被选中，获取其下所有文档ID
        const kbId = keyStr.substring(3);
        const kbDocuments = this.getDocumentsInKnowledgeBase(kbId, treeData);
        kbDocuments.forEach(docId => documentIds.add(docId));
      } else if (!isNaN(key) && keyStr.length > 0) {
        // 直接选中的文档节点
        documentIds.add(keyStr);
      }
    });

    return Array.from(documentIds);
  },

  /**
   * 获取知识库下的所有文档ID
   * @param {String} knowledgeBaseId - 知识库ID
   * @param {Array} treeData - 树数据
   * @returns {Array} 文档ID数组
   */
  getDocumentsInKnowledgeBase(knowledgeBaseId, treeData) {
    const kbNodeId = `kb_${knowledgeBaseId}`;

    const findKbNode = (nodes) => {
      for (const node of nodes) {
        if (node.id === kbNodeId) {
          return node;
        }
        if (node.children) {
          const found = findKbNode(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const kbNode = findKbNode(treeData);
    if (!kbNode || !kbNode.children) {
      return [];
    }

    return kbNode.children
      .filter(child => child.type === 'document' && !isNaN(child.id))
      .map(child => child.id.toString());
  },  /**
   * 验证权限数据的合法性
   * @param {Array} nodeIds - 节点ID数组
   * @returns {Object} 验证结果
   */
  validatePermissionData(nodeIds) {
    const result = {
      isValid: true,
      documentCount: 0,
      invalidIds: [],
      message: ''
    };

    if (!nodeIds || !Array.isArray(nodeIds)) {
      result.isValid = false;
      result.message = '权限数据格式错误';
      return result;
    }

    nodeIds.forEach(id => {
      const idStr = id.toString();
      if (isNaN(id) || idStr.length === 0) {
        result.invalidIds.push(id);
      } else {
        result.documentCount++;
      }
    });

    if (result.invalidIds.length > 0) {
      result.isValid = false;
      result.message = `发现无效的文档ID: ${result.invalidIds.join(', ')}`;
    } else if (result.documentCount === 0) {
      result.message = '未选择任何文档权限';
    } else {
      result.message = `已选择 ${result.documentCount} 个文档权限`;
    }

    return result;
  },

  /**
   * 设置树的选中状态，支持知识库全选功能
   * @param {Object} treeRef - 树组件引用
   * @param {Array} documentIds - 文档ID数组
   * @param {Array} treeData - 完整的树数据
   */
  setTreeCheckedKeys(treeRef, documentIds, treeData) {
    if (!treeRef || !documentIds) return;

    // 首先设置文档节点选中
    treeRef.setCheckedKeys(documentIds);

    // 检查是否需要设置知识库节点选中（当知识库下所有文档都被选中时）
    const kbsToCheck = this.getKnowledgeBasesToCheck(documentIds, treeData);

    // 获取当前已选中的节点，包括新的知识库节点
    const currentChecked = treeRef.getCheckedKeys();
    const allChecked = [...new Set([...currentChecked, ...kbsToCheck])];

    treeRef.setCheckedKeys(allChecked);
  },

  /**
   * 获取应该被选中的知识库节点（当其下所有文档都被选中时）
   * @param {Array} documentIds - 已选中的文档ID数组
   * @param {Array} treeData - 完整的树数据
   * @returns {Array} 应该被选中的知识库节点ID数组
   */
  getKnowledgeBasesToCheck(documentIds, treeData) {
    const kbsToCheck = [];

    treeData.forEach(kbNode => {
      if (kbNode.id && kbNode.id.toString().startsWith('kb_') && kbNode.children) {
        const allDocIds = kbNode.children
          .filter(child => child.type === 'document' && !isNaN(child.id))
          .map(child => child.id.toString());

        // 检查这个知识库下的所有文档是否都被选中
        const allSelected = allDocIds.every(docId => documentIds.includes(docId));

        if (allSelected && allDocIds.length > 0) {
          kbsToCheck.push(kbNode.id);
        }
      }
    });

    return kbsToCheck;
  },

  /**
   * 格式化权限数据用于显示
   * @param {Array} documentIds - 文档ID数组
   * @param {Array} treeData - 完整的树数据
   * @returns {Array} 格式化后的显示数据
   */
  formatPermissionDisplay(documentIds, treeData) {
    const permissions = [];

    const findDocumentInfo = (nodes, targetId) => {
      for (const node of nodes) {
        if (node.id && node.id.toString() === targetId.toString() && node.type === 'document') {
          return {
            id: node.id,
            name: node.name,
            knowledgeBaseName: findKnowledgeBaseName(treeData, node.knowledgeBaseId)
          };
        }
        if (node.children) {
          const found = findDocumentInfo(node.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const findKnowledgeBaseName = (nodes, kbId) => {
      for (const node of nodes) {
        if (node.id === `kb_${kbId}`) {
          return node.name;
        }
      }
      return '未知知识库';
    };

    documentIds.forEach(docId => {
      const docInfo = findDocumentInfo(treeData, docId);
      if (docInfo) {
        permissions.push(docInfo);
      }
    });

    return permissions;
  }
};

export default PermissionTreeHelper;
