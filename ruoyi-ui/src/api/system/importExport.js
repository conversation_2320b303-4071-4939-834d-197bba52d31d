import request from '@/utils/request'

// 下载机构导入模板
export function downloadTemplate() {
  return request({
    url: '/system/import-export/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入机构和用户数据
export function importData(data) {
  return request({
    url: '/system/import-export/import',
    method: 'post',
    data: data
  })
}

// 导出机构和用户数据
export function exportData(query) {
  return request({
    url: '/system/import-export/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取导入历史记录
export function getImportHistory(query) {
  return request({
    url: '/system/import-export/history',
    method: 'get',
    params: query
  })
}
