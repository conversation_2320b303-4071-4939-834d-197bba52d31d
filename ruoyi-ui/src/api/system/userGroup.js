import request from '@/utils/request'

// 查询用户组列表
export function listUserGroup(query) {
  return request({
    url: '/user-group/list',
    method: 'get',
    params: query
  })
}

// 查询用户组详细
export function getUserGroup(id) {
  return request({
    url: '/user-group/' + id,
    method: 'get'
  })
}

// 新增用户组
export function addUserGroup(data) {
  return request({
    url: '/user-group',
    method: 'post',
    data: data
  })
}

// 修改用户组
export function updateUserGroup(data) {
  return request({
    url: '/user-group',
    method: 'put',
    data: data
  })
}

// 删除用户组
export function delUserGroup(id) {
  return request({
    url: '/user-group/' + id,
    method: 'delete'
  })
}
