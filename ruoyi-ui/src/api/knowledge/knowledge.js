import request from '@/utils/request'

// 查询知识库列表
export function listKnowledge(query) {
  return request({
    url: '/knowledge/list',
    method: 'get',
    params: query
  })
}

// 查询知识库详细
export function getKnowledge(id) {
  return request({
    url: '/knowledge/' + id,
    method: 'get'
  })
}

// 新增知识库
export function addKnowledge(data) {
  return request({
    url: '/knowledge',
    method: 'post',
    data: data
  })
}

// 修改知识库
export function updateKnowledge(data) {
  return request({
    url: '/knowledge',
    method: 'put',
    data: data
  })
}

// 删除知识库
export function delKnowledge(id) {
  return request({
    url: '/knowledge/' + id,
    method: 'delete'
  })
}

// 获取知识库文档列表
export function getKnowledgeDocuments(knowledgeId, query) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents',
    method: 'get',
    params: query
  })
}

// 上传文档到知识库
export function uploadDocument(knowledgeId, data) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents/upload',
    method: 'post',
    data: data
  })
}

// 解析文档
export function parseDocument(knowledgeId, docId) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents/' + docId + '/parse',
    method: 'put'
  })
}

// 删除文档
export function deleteDocument(knowledgeId, docId) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents/' + docId,
    method: 'delete'
  })
}

// 批量解析文档
export function batchParseDocuments(knowledgeId, docIds) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents/batch-parse',
    method: 'post',
    data: { docIds }
  })
}

// 批量删除文档
export function batchDeleteDocuments(knowledgeId, docIds) {
  return request({
    url: '/knowledge/' + knowledgeId + '/documents/batch-delete',
    method: 'delete',
    data: { docIds }
  })
}

// 获取知识库权限树
export function getKnowledgePermissionTree() {
  return request({
    url: '/knowledge/permission/tree',
    method: 'get'
  })
}

// 获取机构权限节点
export function getOrgPermissionNodes(orgId) {
  return request({
    url: '/knowledge/permission/org',
    method: 'get',
    params: { orgId: orgId }
  })
}

// 设置机构权限节点
export function setOrgPermissionNodes(data) {
  return request({
    url: '/knowledge/permission/org',
    method: 'post',
    data: data
  })
}

// 获取用户组权限节点
export function getUserGroupPermissionNodes(userGroupId) {
  return request({
    url: '/knowledge/permission/user-group',
    method: 'get',
    params: { userGroupId: userGroupId }
  })
}

// 设置用户组权限节点
export function setUserGroupPermissionNodes(data) {
  return request({
    url: '/knowledge/permission/user-group',
    method: 'post',
    data: data
  })
}

// 获取机构知识库权限（兼容旧版本）
export function getOrgKnowledgePermission(query) {
  return request({
    url: '/knowledge/permission/org',
    method: 'get',
    params: query
  })
}

// 设置机构知识库权限（兼容旧版本）
export function setOrgKnowledgePermission(data) {
  return request({
    url: '/knowledge/permission/org',
    method: 'post',
    data: data
  })
}

// 获取用户组知识库权限（兼容旧版本）
export function getUserGroupKnowledgePermission(query) {
  return request({
    url: '/knowledge/permission/user-group',
    method: 'get',
    params: query
  })
}

// 设置用户组知识库权限（兼容旧版本）
export function setUserGroupKnowledgePermission(data) {
  return request({
    url: '/knowledge/permission/user-group',
    method: 'post',
    data: data
  })
}

// 获取文档调用日志
export function getDocumentCallLogs(query) {
  return request({
    url: '/knowledge/logs/document-call',
    method: 'get',
    params: query
  })
}

// 获取文档下载日志
export function getDocumentDownloadLogs(query) {
  return request({
    url: '/knowledge/logs/document-download',
    method: 'get',
    params: query
  })
}
