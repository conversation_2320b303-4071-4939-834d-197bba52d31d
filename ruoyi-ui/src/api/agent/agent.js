import request from '@/utils/request'

// 查询智能体列表
export function listAgent(query) {
  return request({
    url: '/agent/list',
    method: 'get',
    params: query
  })
}

// 查询智能体详细
export function getAgent(id) {
  return request({
    url: '/agent/' + id,
    method: 'get'
  })
}

// 新增智能体
export function addAgent(data) {
  return request({
    url: '/agent',
    method: 'post',
    data: data
  })
}

// 修改智能体
export function updateAgent(data) {
  return request({
    url: '/agent',
    method: 'put',
    data: data
  })
}

// 删除智能体
export function delAgent(id) {
  return request({
    url: '/agent/' + id,
    method: 'delete'
  })
}

// 获取机构智能体权限
export function getOrgAgentPermission(query) {
  return request({
    url: '/agent/permission/org',
    method: 'get',
    params: query
  })
}

// 设置机构智能体权限
export function setOrgAgentPermission(data) {
  return request({
    url: '/agent/permission/org',
    method: 'post',
    data: data
  })
}

// 获取用户组智能体权限
export function getUserGroupAgentPermission(query) {
  return request({
    url: '/agent/permission/user-group',
    method: 'get',
    params: query
  })
}

// 设置用户组智能体权限
export function setUserGroupAgentPermission(data) {
  return request({
    url: '/agent/permission/user-group',
    method: 'post',
    data: data
  })
}
