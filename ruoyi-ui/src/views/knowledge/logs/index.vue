<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="文档调用日志" name="call">
        <el-form :model="callQueryParams" ref="callQueryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="文档名称" prop="documentName">
            <el-input
              v-model="callQueryParams.documentName"
              placeholder="请输入文档名称"
              clearable
              @keyup.enter.native="handleCallQuery"
            />
          </el-form-item>
          <el-form-item label="问题内容" prop="question">
            <el-input
              v-model="callQueryParams.question"
              placeholder="请输入问题内容"
              clearable
              @keyup.enter.native="handleCallQuery"
            />
          </el-form-item>
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="callQueryParams.userName"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleCallQuery"
            />
          </el-form-item>
          <el-form-item label="调用时间">
            <el-date-picker
              v-model="callDateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCallQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetCallQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="callLoading" :data="callLogList">
          <el-table-column label="文档名称" align="center" prop="documentName" :show-overflow-tooltip="true" />
          <el-table-column label="问题内容" align="center" prop="question" :show-overflow-tooltip="true" />
          <el-table-column label="用户名" align="center" prop="userName" />
          <el-table-column label="调用时间" align="center" prop="callTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.callTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP地址" align="center" prop="ipAddress" />
        </el-table>

        <pagination
          v-show="callTotal>0"
          :total="callTotal"
          :page.sync="callQueryParams.pageNum"
          :limit.sync="callQueryParams.pageSize"
          @pagination="getCallLogList"
        />
      </el-tab-pane>

      <el-tab-pane label="文档下载日志" name="download">
        <el-form :model="downloadQueryParams" ref="downloadQueryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="文档名称" prop="documentName">
            <el-input
              v-model="downloadQueryParams.documentName"
              placeholder="请输入文档名称"
              clearable
              @keyup.enter.native="handleDownloadQuery"
            />
          </el-form-item>
          <el-form-item label="下载用户" prop="userName">
            <el-input
              v-model="downloadQueryParams.userName"
              placeholder="请输入下载用户"
              clearable
              @keyup.enter.native="handleDownloadQuery"
            />
          </el-form-item>
          <el-form-item label="下载时间">
            <el-date-picker
              v-model="downloadDateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleDownloadQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetDownloadQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="downloadLoading" :data="downloadLogList">
          <el-table-column label="文档名称" align="center" prop="documentName" :show-overflow-tooltip="true" />
          <el-table-column label="下载用户" align="center" prop="userName" />
          <el-table-column label="下载时间" align="center" prop="downloadTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.downloadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP地址" align="center" prop="ipAddress" />
          <el-table-column label="文件大小" align="center" prop="fileSize">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.fileSize) }}
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="downloadTotal>0"
          :total="downloadTotal"
          :page.sync="downloadQueryParams.pageNum"
          :limit.sync="downloadQueryParams.pageSize"
          @pagination="getDownloadLogList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getDocumentCallLogs, getDocumentDownloadLogs } from "@/api/knowledge/knowledge";

export default {
  name: "KnowledgeLogs",
  data() {
    return {
      // 当前激活的标签页
      activeTab: "call",
      // 显示搜索条件
      showSearch: true,

      // 调用日志相关
      callLoading: true,
      callTotal: 0,
      callLogList: [],
      callDateRange: [],
      callQueryParams: {
        pageNum: 1,
        pageSize: 10,
        documentName: null,
        question: null,
        userName: null,
      },

      // 下载日志相关
      downloadLoading: true,
      downloadTotal: 0,
      downloadLogList: [],
      downloadDateRange: [],
      downloadQueryParams: {
        pageNum: 1,
        pageSize: 10,
        documentName: null,
        userName: null,
      }
    };
  },
  created() {
    this.getCallLogList();
    this.getDownloadLogList();
  },
  methods: {
    /** 标签页切换 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
    },

    /** 查询调用日志列表 */
    getCallLogList() {
      this.callLoading = true;
      getDocumentCallLogs(this.addDateRange(this.callQueryParams, this.callDateRange)).then(response => {
        this.callLogList = response.rows;
        this.callTotal = response.total;
        this.callLoading = false;
      });
    },

    /** 查询下载日志列表 */
    getDownloadLogList() {
      this.downloadLoading = true;
      getDocumentDownloadLogs(this.addDateRange(this.downloadQueryParams, this.downloadDateRange)).then(response => {
        this.downloadLogList = response.rows;
        this.downloadTotal = response.total;
        this.downloadLoading = false;
      });
    },

    /** 搜索调用日志 */
    handleCallQuery() {
      this.callQueryParams.pageNum = 1;
      this.getCallLogList();
    },

    /** 重置调用日志查询 */
    resetCallQuery() {
      this.callDateRange = [];
      this.resetForm("callQueryForm");
      this.handleCallQuery();
    },

    /** 搜索下载日志 */
    handleDownloadQuery() {
      this.downloadQueryParams.pageNum = 1;
      this.getDownloadLogList();
    },

    /** 重置下载日志查询 */
    resetDownloadQuery() {
      this.downloadDateRange = [];
      this.resetForm("downloadQueryForm");
      this.handleDownloadQuery();
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return Math.round(size / 1024) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return Math.round(size / (1024 * 1024)) + ' MB';
      } else {
        return Math.round(size / (1024 * 1024 * 1024)) + ' GB';
      }
    }
  }
};
</script>
