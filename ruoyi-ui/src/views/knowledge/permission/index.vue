<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="机构知识库权限" name="org">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="head-container">
              <h4>机构组织</h4>
              <el-tree
                :data="orgOptions"
                :props="orgProps"
                :expand-on-click-node="false"
                node-key="id"
                ref="orgTree"
                default-expand-all
                highlight-current
                @node-click="handleOrgNodeClick"
              />
            </div>
          </el-col>
          <el-col :span="16">
            <div class="head-container" v-loading="loading">
              <h4>知识库权限</h4>
              <el-form :model="orgForm" ref="orgForm" label-width="100px">
                <el-form-item label="选择机构">
                  <el-input v-model="orgForm.orgName" readonly />
                </el-form-item>
                <el-form-item label="授权知识库">
                  <el-tree
                    :data="knowledgeTreeOptions"
                    :props="knowledgeTreeProps"
                    node-key="id"
                    ref="knowledgeTree"
                    show-checkbox
                    default-expand-all
                    :default-checked-keys="orgForm.selectedNodeIds"
                    :check-strictly="false"
                    check-on-click-node
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitOrgForm" :loading="loading">保 存</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="用户组知识库权限" name="userGroup">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="head-container">
              <h4>用户组</h4>
              <el-tree
                :data="userGroupOptions"
                :props="userGroupProps"
                node-key="id"
                ref="userGroupTree"
                default-expand-all
                highlight-current
                @node-click="handleUserGroupNodeClick"
              />
            </div>
          </el-col>
          <el-col :span="16">
            <div class="head-container" v-loading="loading">
              <h4>知识库权限</h4>
              <el-form :model="userGroupForm" ref="userGroupForm" label-width="100px">
                <el-form-item label="选择用户组">
                  <el-input v-model="userGroupForm.userGroupName" readonly />
                </el-form-item>
                <el-form-item label="授权知识库">
                  <el-tree
                    :data="knowledgeTreeOptions"
                    :props="knowledgeTreeProps"
                    node-key="id"
                    ref="userGroupKnowledgeTree"
                    show-checkbox
                    default-expand-all
                    :default-checked-keys="userGroupForm.selectedNodeIds"
                    :check-strictly="false"
                    check-on-click-node
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitUserGroupForm" :loading="loading">保 存</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getKnowledgePermissionTree,
  getOrgPermissionNodes,
  setOrgPermissionNodes,
  getUserGroupPermissionNodes,
  setUserGroupPermissionNodes
} from "@/api/knowledge/knowledge";
import { listUserGroup } from "@/api/system/userGroup";
import { deptTreeSelect } from "@/api/system/user";
import PermissionTreeHelper from "@/utils/permissionTreeHelper";

export default {
  name: "KnowledgePermission",
  data() {
    return {
      // 当前激活的标签页
      activeTab: "org",
      // 加载状态
      loading: false,
      // 机构树选项
      orgOptions: [],
      orgProps: {
        children: 'children',
        label: 'label'
      },
      // 用户组选项
      userGroupOptions: [],
      userGroupProps: {
        children: 'children',
        label: 'name'
      },
      // 知识库树选项
      knowledgeTreeOptions: [],
      // 原始树数据（用于权限处理）
      originalTreeData: [],
      knowledgeTreeProps: {
        children: 'children',
        label: 'name'
      },
      // 机构表单
      orgForm: {
        orgId: null,
        orgName: "",
        selectedNodeIds: []
      },
      // 用户组表单
      userGroupForm: {
        userGroupId: null,
        userGroupName: "",
        selectedNodeIds: []
      }
    };
  },
  created() {
    this.getOrgTree();
    this.getUserGroupTree();
    this.getKnowledgeTree();
  },
  methods: {
    /** 获取机构树 */
    getOrgTree() {
      deptTreeSelect().then(response => {
        this.orgOptions = response.data;
      });
    },
    /** 获取用户组树 */
    getUserGroupTree() {
      listUserGroup().then(response => {
        this.userGroupOptions = response.rows || [];
      });
    },
    /** 获取知识库树 */
    getKnowledgeTree() {
      getKnowledgePermissionTree().then(response => {
        // 保存原始树数据用于权限处理
        this.knowledgeTreeOptions = PermissionTreeHelper.processTreeData(response.data || []);
        this.originalTreeData = response.data || [];
      });
    },
    /** 标签页切换 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
    },
    /** 机构节点点击 */
    handleOrgNodeClick(data) {
      this.loading = true;
      this.orgForm.orgId = data.id;
      this.orgForm.orgName = data.label;
      this.getOrgPermissionNodes(data.id);
    },
    /** 用户组节点点击 */
    handleUserGroupNodeClick(data) {
      this.loading = true;
      this.userGroupForm.userGroupId = data.id;
      this.userGroupForm.userGroupName = data.name;
      this.getUserGroupPermissionNodes(data.id);
    },
    /** 获取机构权限节点 */
    getOrgPermissionNodes(orgId) {
      getOrgPermissionNodes(orgId).then(response => {
        this.orgForm.selectedNodeIds = response.data || [];
        this.$nextTick(() => {
          // 使用工具类设置树的选中状态，支持知识库全选显示
          PermissionTreeHelper.setTreeCheckedKeys(
            this.$refs.knowledgeTree,
            this.orgForm.selectedNodeIds,
            this.originalTreeData
          );
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取用户组权限节点 */
    getUserGroupPermissionNodes(userGroupId) {
      getUserGroupPermissionNodes(userGroupId).then(response => {
        this.userGroupForm.selectedNodeIds = response.data || [];
        this.$nextTick(() => {
          // 使用工具类设置树的选中状态，支持知识库全选显示
          PermissionTreeHelper.setTreeCheckedKeys(
            this.$refs.userGroupKnowledgeTree,
            this.userGroupForm.selectedNodeIds,
            this.originalTreeData
          );
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 提交机构权限表单 */
    submitOrgForm() {
      if (!this.orgForm.orgId) {
        this.$modal.msgError("请选择机构");
        return;
      }

      this.loading = true;
      const checkedKeys = this.$refs.knowledgeTree.getCheckedKeys();
      const halfCheckedKeys = this.$refs.knowledgeTree.getHalfCheckedKeys();
      const allSelectedKeys = checkedKeys.concat(halfCheckedKeys);

      // 使用工具类过滤出实际的文档ID（知识库选择会转换为其下所有文档）
      const documentIds = PermissionTreeHelper.filterDocumentIds(allSelectedKeys, this.originalTreeData);

      // 验证权限数据
      const validation = PermissionTreeHelper.validatePermissionData(documentIds);
      if (!validation.isValid) {
        this.$modal.msgError(validation.message);
        this.loading = false;
        return;
      }

      const requestData = {
        orgId: this.orgForm.orgId,
        orgName: this.orgForm.orgName,
        nodeIds: documentIds
      };

      setOrgPermissionNodes(requestData).then(response => {
        this.$modal.msgSuccess(`保存成功！${validation.message}`);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 提交用户组权限表单 */
    submitUserGroupForm() {
      if (!this.userGroupForm.userGroupId) {
        this.$modal.msgError("请选择用户组");
        return;
      }

      this.loading = true;
      const checkedKeys = this.$refs.userGroupKnowledgeTree.getCheckedKeys();
      const halfCheckedKeys = this.$refs.userGroupKnowledgeTree.getHalfCheckedKeys();
      const allSelectedKeys = checkedKeys.concat(halfCheckedKeys);

      // 使用工具类过滤出实际的文档ID（知识库选择会转换为其下所有文档）
      const documentIds = PermissionTreeHelper.filterDocumentIds(allSelectedKeys, this.originalTreeData);

      // 验证权限数据
      const validation = PermissionTreeHelper.validatePermissionData(documentIds);
      if (!validation.isValid) {
        this.$modal.msgError(validation.message);
        this.loading = false;
        return;
      }

      const requestData = {
        userGroupId: this.userGroupForm.userGroupId,
        userGroupName: this.userGroupForm.userGroupName,
        nodeIds: documentIds
      };

      setUserGroupPermissionNodes(requestData).then(response => {
        this.$modal.msgSuccess(`保存成功！${validation.message}`);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped>
/* 权限管理页面样式优化 */
.app-container {
  padding: 20px;
}

.head-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.head-container h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

/* 优化树组件样式 */
.el-tree {
  background: transparent;
}

/* 知识库节点样式（不可选择的分组节点） */
.el-tree-node__content {
  height: 32px;
  line-height: 32px;
}

.el-tree-node.is-disabled > .el-tree-node__content {
  color: #606266;
  font-weight: 600;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 4px;
}

.el-tree-node.is-disabled > .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

/* 文档节点样式 */
.el-tree-node:not(.is-disabled) > .el-tree-node__content {
  padding-left: 24px;
}

.el-tree-node:not(.is-disabled) > .el-tree-node__content:hover {
  background-color: #ecf5ff;
}

/* 选中状态样式 */
.el-tree-node.is-current > .el-tree-node__content {
  background-color: #409eff;
  color: white;
}

/* 表单优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 600;
  color: #2c3e50;
}

/* 按钮样式 */
.el-button {
  border-radius: 4px;
  padding: 10px 20px;
}

/* 加载状态样式 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 标签页优化 */
.el-tabs__header {
  margin: 0 0 24px 0;
}

.el-tabs__item {
  font-weight: 600;
  font-size: 14px;
}

.el-tabs__item.is-active {
  color: #409eff;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }

  .head-container {
    padding: 16px;
  }
}
</style>
