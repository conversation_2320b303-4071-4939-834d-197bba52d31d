<template>
  <div class="app-container">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>
        <el-link type="primary" @click="goBack">知识库管理</el-link>
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ knowledgeName }}</el-breadcrumb-item>
    </el-breadcrumb>

    <el-row :gutter="10" class="mb8" style="margin-top: 20px;">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleUpload"
          v-hasPermi="['knowledge:knowledge:upload']"
        >新增文件</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleBatchParse"
          v-hasPermi="['knowledge:knowledge:parse']"
        >批量解析</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          v-hasPermi="['knowledge:knowledge:remove']"
        >批量删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档名称" align="center" prop="fileName" :show-overflow-tooltip="true" />
      <el-table-column label="文档类型" align="center" prop="fileType" />
      <el-table-column label="文档大小" align="center" prop="fileSize">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column label="解析状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '2'" type="success">已解析</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="warning">解析中</el-tag>
          <el-tag v-else-if="scope.row.status === '3'" type="danger">解析失败</el-tag>
          <el-tag v-else type="info">未解析</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleParse(scope.row)"
            v-hasPermi="['knowledge:knowledge:parse']"
            v-if="scope.row.status !== '1'"
          >解析</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['knowledge:knowledge:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 上传文件对话框 -->
    <el-dialog title="上传文件" :visible.sync="uploadDialogVisible" width="600px" append-to-body>
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-remove="handleRemove"
        :before-upload="beforeUpload"
        :auto-upload="false"
        name="files"
        multiple
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          支持.doc、.docx、.pdf、.xls、.xlsx等格式，文件大小不超过1GB
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpload">确 定</el-button>
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getKnowledgeDocuments, uploadDocument, parseDocument, deleteDocument, batchParseDocuments, batchDeleteDocuments } from "@/api/knowledge/knowledge";
import { getToken } from "@/utils/auth";

export default {
  name: "KnowledgeDocuments",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文档表格数据
      documentList: [],
      // 知识库ID
      knowledgeId: null,
      // 知识库名称
      knowledgeName: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 上传对话框
      uploadDialogVisible: false,
      // 上传文件列表
      fileList: [],
      // 上传路径
      uploadUrl: process.env.VUE_APP_BASE_API + "/knowledge/" + this.$route.params.id + "/documents/upload",
      // 上传头部
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      // 上传数据
      uploadData: {}
    };
  },
  created() {
    this.knowledgeId = this.$route.params.id;
    this.uploadUrl = process.env.VUE_APP_BASE_API + "/knowledge/" + this.knowledgeId + "/documents/upload";
    this.getList();
  },
  methods: {
    /** 查询文档列表 */
    getList() {
      this.loading = true;
      getKnowledgeDocuments(this.knowledgeId, this.queryParams).then(response => {
        this.documentList = response.rows;
        this.total = response.total;
        this.knowledgeName = response.knowledgeName;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 返回知识库列表 */
    goBack() {
      this.$router.go(-1);
    },
    /** 上传文件 */
    handleUpload() {
      this.uploadDialogVisible = true;
      this.fileList = [];
    },
    /** 解析文档 */
    handleParse(row) {
      this.$modal.confirm('是否确认解析文档"' + row.fileName + '"？').then(function() {
        return parseDocument(this.knowledgeId, row.id);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess("解析成功");
      }).catch(() => {});
    },
    /** 删除文档 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除文档"' + row.fileName + '"？').then(function() {
        return deleteDocument(this.knowledgeId, ids);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 批量解析 */
    handleBatchParse() {
      this.$modal.confirm('是否确认批量解析选中的文档？').then(function() {
        return batchParseDocuments(this.knowledgeId, this.ids);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量解析成功");
      }).catch(() => {});
    },
    /** 批量删除 */
    handleBatchDelete() {
      this.$modal.confirm('是否确认批量删除选中的文档？').then(function() {
        return batchDeleteDocuments(this.knowledgeId, this.ids);
      }.bind(this)).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量删除成功");
      }).catch(() => {});
    },
    /** 上传前检查 */
    beforeUpload(file) {
      const allowedTypes = ['.doc', '.docx', '.pdf', '.xls', '.xlsx'];
      const fileName = file.name.toLowerCase();
      const isAllowedType = allowedTypes.some(type => fileName.endsWith(type));

      if (!isAllowedType) {
        this.$modal.msgError('只能上传.doc、.docx、.pdf、.xls、.xlsx格式的文件!');
        return false;
      }

      const isLt1G = file.size / 1024 / 1024 / 1024 < 1;
      if (!isLt1G) {
        this.$modal.msgError('上传文件大小不能超过1GB!');
        return false;
      }

      return true;
    },
    /** 提交上传 */
    submitUpload() {
      this.$refs.upload.submit();
    },
    /** 上传成功 */
    handleUploadSuccess(response, file) {
      this.$modal.msgSuccess("上传成功");
      this.uploadDialogVisible = false;
      this.getList();
    },
    /** 上传失败 */
    handleUploadError(err, file) {
      this.$modal.msgError("上传失败");
    },
    /** 移除文件 */
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return Math.round(size / 1024) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return Math.round(size / (1024 * 1024)) + ' MB';
      } else {
        return Math.round(size / (1024 * 1024 * 1024)) + ' GB';
      }
    }
  }
};
</script>
