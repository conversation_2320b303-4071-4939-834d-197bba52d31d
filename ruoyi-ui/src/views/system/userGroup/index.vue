<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户组名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入用户组名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:userGroup:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:userGroup:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:userGroup:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userGroupList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户组名称" align="center" prop="name" />
      <el-table-column label="所属机构" align="center" prop="orgName">
        <template slot-scope="scope">
          <span>{{ getOrgNameById(scope.row.orgId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户数量" align="center" prop="memberCount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:userGroup:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:userGroup:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="用户组名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入用户组名称" />
        </el-form-item>
        <el-form-item label="所属机构" prop="orgId">
          <treeselect
            v-model="form.orgId"
            :options="orgOptions"
            :normalizer="normalizer"
            placeholder="请选择所属机构"
          />
        </el-form-item>
        <el-form-item label="选择用户" prop="userIds">
          <!-- 调试信息 -->
          <div style="margin-bottom: 10px; font-size: 12px; color: #999;">
            当前选中的用户IDs: {{ form.userIds }}
          </div>
          <el-checkbox-group v-model="form.userIds" class="user-checkbox-group">
            <el-checkbox
              v-for="user in validUserOptions"
              :key="user.userId"
              :label="parseInt(user.userId)"
              class="user-checkbox"
            >
              {{ user.userName }}（{{ user.nickName }}）- ID: {{ user.userId }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserGroup, getUserGroup, delUserGroup, addUserGroup, updateUserGroup } from "@/api/system/userGroup";
import { deptTreeSelect, listUser } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
  name: "UserGroup",
  components: { Treeselect },
  computed: {
    // 过滤有效的用户选项
    validUserOptions() {
      return this.userOptions.filter(user =>
        user &&
        user.userId &&
        user.userId !== null &&
        user.userId !== '' &&
        !isNaN(parseInt(user.userId)) &&
        parseInt(user.userId) > 0
      );
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户组表格数据
      userGroupList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 机构选项
      orgOptions: [],
      // 机构映射表
      orgMap: {},
      // 用户选项
      userOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "用户组名称不能为空", trigger: "blur" }
        ],
        orgId: [
          { required: true, message: "所属机构不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getOrgTree();
    this.getUserOptions();
  },
  methods: {
    /** 查询用户组列表 */
    getList() {
      this.loading = true;
      listUserGroup(this.queryParams).then(response => {
        this.userGroupList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取机构树 */
    getOrgTree() {
      deptTreeSelect().then(response => {
        this.orgOptions = response.data;
        // 构建机构映射表
        this.buildOrgMap(response.data);
      });
    },
    /** 构建机构映射表 */
    buildOrgMap(deptList) {
      this.orgMap = {};
      const traverse = (nodes) => {
        if (!nodes) return;
        nodes.forEach(node => {
          this.orgMap[node.deptId || node.id] = node.deptName || node.label;
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        });
      };
      traverse(deptList);
    },
    /** 根据机构ID获取机构名称 */
    getOrgNameById(orgId) {
      return this.orgMap[orgId] || '未知机构';
    },
    /** 转换机构数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId || node.id,
        label: node.deptName || node.label,
        children: node.children
      }
    },
    /** 获取用户选项 */
    getUserOptions() {
      listUser().then(response => {
        const rawUsers = response.rows || response.data || [];
        console.log('原始用户数据:', rawUsers);

        // 过滤和验证用户数据
        this.userOptions = rawUsers.filter(user => {
          if (!user || !user.userId) {
            console.warn('发现无效用户数据:', user);
            return false;
          }

          const userId = parseInt(user.userId);
          if (isNaN(userId) || userId <= 0) {
            console.warn('发现无效用户ID:', user.userId, user);
            return false;
          }

          return true;
        });

        console.log('过滤后的用户数据:', this.userOptions);

        // 检查是否有重复的用户ID
        const userIds = this.userOptions.map(user => user.userId);
        const uniqueIds = [...new Set(userIds)];
        if (userIds.length !== uniqueIds.length) {
          console.warn('发现重复的用户ID:', userIds);
        }
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.userOptions = [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        orgId: null,
        userIds: []
      };
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户组";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUserGroup(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户组";

        console.log('获取到的用户组数据:', response.data);

        // 处理 userIds 数据
        if (this.form.userIds && Array.isArray(this.form.userIds)) {
          // 将字符串ID转换为数字ID，并过滤无效值
          this.form.userIds = this.form.userIds
            .map(id => {
              const numId = parseInt(id);
              console.log('转换用户ID:', id, '->', numId);
              return numId;
            })
            .filter(id => !isNaN(id) && id > 0);
        } else if (this.form.userIds && !Array.isArray(this.form.userIds)) {
          // 如果不是数组，转换为数组
          this.form.userIds = [parseInt(this.form.userIds)].filter(id => !isNaN(id) && id > 0);
        } else {
          this.form.userIds = [];
        }

        console.log('处理后的用户IDs:', this.form.userIds);

        // 验证这些用户ID是否在可选列表中
        const availableUserIds = this.validUserOptions.map(user => parseInt(user.userId));
        console.log('可用的用户IDs:', availableUserIds);

        // 过滤掉不在可选列表中的用户ID
        this.form.userIds = this.form.userIds.filter(id => availableUserIds.includes(id));
        console.log('最终的用户IDs:', this.form.userIds);

        // 强制刷新组件
        this.$nextTick(() => {
          console.log('下一个tick后的用户IDs:', this.form.userIds);
        });
      }).catch(error => {
        console.error('获取用户组详情失败:', error);
        this.$modal.msgError("获取用户组信息失败");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证和清理用户IDs
          if (this.form.userIds && Array.isArray(this.form.userIds)) {
            // 过滤掉无效值
            this.form.userIds = this.form.userIds.filter(id =>
              id !== null &&
              id !== undefined &&
              id !== '' &&
              !isNaN(parseInt(id)) &&
              parseInt(id) > 0
            );

            // 确保所有ID都是数字类型
            this.form.userIds = this.form.userIds.map(id => parseInt(id));
          } else {
            this.form.userIds = [];
          }

          console.log('提交的表单数据:', this.form);
          console.log('最终的用户IDs:', this.form.userIds);

          if (this.form.id != null) {
            updateUserGroup(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改用户组失败:', error);
              this.$modal.msgError("修改失败：" + (error.msg || error.message || '未知错误'));
            });
          } else {
            addUserGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增用户组失败:', error);
              this.$modal.msgError("新增失败：" + (error.msg || error.message || '未知错误'));
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除用户组编号为"' + ids + '"的数据项？').then(function() {
        return delUserGroup(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.user-checkbox-group {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.user-checkbox {
  display: block;
  margin-bottom: 8px;
  width: 100%;
}

.user-checkbox:last-child {
  margin-bottom: 0;
}
</style>
