<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>机构导入导出</span>
      </div>
      <div class="card-body">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>模板下载</span>
              </div>
              <div>
                <p>请先下载导入模板，按照模板格式填写机构和用户信息</p>
                <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">下载模板</el-button>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>数据导入</span>
              </div>
              <div>
                <p>支持批量导入机构和用户数据，请确保数据格式正确</p>
                <el-upload
                  ref="upload"
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :auto-upload="false"
                  accept=".xlsx,.xls"
                >
                  <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
                  <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传并导入</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传xlsx/xls文件，且不超过10MB</div>
                </el-upload>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>数据导出</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="exportData">导出数据</el-button>
              </div>
              <div>
                <p>导出当前系统中的机构和用户数据</p>
                <el-form :model="exportForm" ref="exportForm" :inline="true">
                  <el-form-item label="导出类型">
                    <el-select v-model="exportForm.type" placeholder="请选择导出类型">
                      <el-option label="全部数据" value="all"></el-option>
                      <el-option label="仅机构数据" value="org"></el-option>
                      <el-option label="仅用户数据" value="user"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="exportData">导出</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>导入历史</span>
              </div>
              <el-table :data="historyList" v-loading="historyLoading">
                <el-table-column prop="fileName" label="文件名" />
                <el-table-column prop="importTime" label="导入时间" width="180">
                  <template slot-scope="scope">
                    {{ parseTime(scope.row.importTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                  </template>
                </el-table-column>
                <el-table-column prop="importUser" label="导入人" />
                <el-table-column prop="status" label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.status === 'success'" type="success">成功</el-tag>
                    <el-tag v-else-if="scope.row.status === 'failed'" type="danger">失败</el-tag>
                    <el-tag v-else type="warning">处理中</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="successCount" label="成功数量" width="100" />
                <el-table-column prop="failCount" label="失败数量" width="100" />
                <el-table-column prop="remark" label="备注" />
              </el-table>

              <pagination
                v-show="historyTotal>0"
                :total="historyTotal"
                :page.sync="historyParams.pageNum"
                :limit.sync="historyParams.pageSize"
                @pagination="getHistoryList"
              />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { downloadTemplate, importData, exportData, getImportHistory } from "@/api/system/importExport";
import { getToken } from "@/utils/auth";

export default {
  name: "ImportExport",
  data() {
    return {
      // 上传路径
      uploadUrl: process.env.VUE_APP_BASE_API + "/system/import-export/import",
      // 上传头部
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      // 导出表单
      exportForm: {
        type: "all"
      },
      // 历史记录
      historyLoading: false,
      historyList: [],
      historyTotal: 0,
      historyParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.getHistoryList();
  },
  methods: {
    /** 下载模板 */
    downloadTemplate() {
      downloadTemplate().then(response => {
        this.download(response, "机构导入模板.xlsx");
      });
    },

    /** 文件上传前检查 */
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.$modal.msgError('只能上传Excel文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('文件大小不能超过10MB!');
        return false;
      }
      return true;
    },

    /** 提交上传 */
    submitUpload() {
      this.$refs.upload.submit();
    },

    /** 上传成功回调 */
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess("导入成功");
        this.getHistoryList();
      } else {
        this.$modal.msgError(response.msg || "导入失败");
      }
    },

    /** 上传失败回调 */
    handleUploadError(error) {
      this.$modal.msgError("导入失败，请检查网络连接");
    },

    /** 导出数据 */
    exportData() {
      exportData(this.exportForm).then(response => {
        this.download(response, "机构用户数据.xlsx");
      });
    },

    /** 获取导入历史 */
    getHistoryList() {
      this.historyLoading = true;
      getImportHistory(this.historyParams).then(response => {
        this.historyList = response.rows;
        this.historyTotal = response.total;
        this.historyLoading = false;
      });
    }
  }
};
</script>

<style scoped>
.card-body {
  padding: 20px;
}
</style>
