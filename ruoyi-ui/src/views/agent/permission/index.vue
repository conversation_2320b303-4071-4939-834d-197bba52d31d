<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="机构智能体权限" name="org">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="head-container">
              <h4>机构组织</h4>
              <el-tree
                :data="orgOptions"
                :props="orgProps"
                :expand-on-click-node="false"
                node-key="id"
                ref="orgTree"
                default-expand-all
                highlight-current
                @node-click="handleOrgNodeClick"
              />
            </div>
          </el-col>
          <el-col :span="16">
            <div class="head-container" v-loading="loading">
              <h4>智能体权限</h4>
              <el-form :model="orgForm" ref="orgForm" label-width="100px">
                <el-form-item label="选择机构">
                  <el-input v-model="orgForm.orgName" readonly />
                </el-form-item>
                <el-form-item label="授权智能体">
                  <el-checkbox-group v-model="orgForm.agentIds">
                    <el-checkbox v-for="agent in agentOptions" :key="agent.id" :label="agent.id">
                      {{ agent.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitOrgForm" :loading="loading">保 存</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="用户组智能体权限" name="userGroup">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="head-container">
              <h4>用户组</h4>
              <el-tree
                :data="userGroupOptions"
                :props="userGroupProps"
                node-key="id"
                ref="userGroupTree"
                default-expand-all
                highlight-current
                @node-click="handleUserGroupNodeClick"
              />
            </div>
          </el-col>
          <el-col :span="16">
            <div class="head-container" v-loading="loading">
              <h4>智能体权限</h4>
              <el-form :model="userGroupForm" ref="userGroupForm" label-width="100px">
                <el-form-item label="选择用户组">
                  <el-input v-model="userGroupForm.userGroupName" readonly />
                </el-form-item>
                <el-form-item label="授权智能体">
                  <el-checkbox-group v-model="userGroupForm.agentIds">
                    <el-checkbox v-for="agent in agentOptions" :key="agent.id" :label="agent.id">
                      {{ agent.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitUserGroupForm" :loading="loading">保 存</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { listAgent, getOrgAgentPermission, setOrgAgentPermission, getUserGroupAgentPermission, setUserGroupAgentPermission } from "@/api/agent/agent";
import { listUserGroup } from "@/api/system/userGroup";
import { deptTreeSelect } from "@/api/system/user";

export default {
  name: "AgentPermission",
  data() {
    return {
      // 当前激活的标签页
      activeTab: "org",
      // 加载状态
      loading: false,
      // 机构树选项
      orgOptions: [],
      orgProps: {
        children: 'children',
        label: 'label'
      },
      // 用户组选项
      userGroupOptions: [],
      userGroupProps: {
        children: 'children',
        label: 'name'
      },
      // 智能体选项
      agentOptions: [],
      // 机构表单
      orgForm: {
        orgId: null,
        orgName: "",
        agentIds: []
      },
      // 用户组表单
      userGroupForm: {
        userGroupId: null,
        userGroupName: "",
        agentIds: []
      }
    };
  },
  created() {
    this.getOrgTree();
    this.getUserGroupTree();
    this.getAgentOptions();
  },
  methods: {
    /** 获取机构树 */
    getOrgTree() {
      deptTreeSelect().then(response => {
        this.orgOptions = response.data;
      });
    },
    /** 获取用户组树 */
    getUserGroupTree() {
      listUserGroup().then(response => {
        this.userGroupOptions = response.rows || [];
      });
    },
    /** 获取智能体选项 */
    getAgentOptions() {
      listAgent().then(response => {
        this.agentOptions = response.rows || [];
      });
    },
    /** 标签页切换 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
    },
    /** 机构节点点击 */
    handleOrgNodeClick(data) {
      this.loading = true;
      this.orgForm.orgId = data.id;
      this.orgForm.orgName = data.label;
      this.getOrgAgentPermission(data.id);
    },
    /** 用户组节点点击 */
    handleUserGroupNodeClick(data) {
      this.loading = true;
      this.userGroupForm.userGroupId = data.id;
      this.userGroupForm.userGroupName = data.name;
      this.getUserGroupAgentPermission(data.id);
    },
    /** 获取机构智能体权限 */
    getOrgAgentPermission(orgId) {
      getOrgAgentPermission({ orgId: orgId }).then(response => {
        this.orgForm.agentIds = response.data || [];
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取用户组智能体权限 */
    getUserGroupAgentPermission(userGroupId) {
      getUserGroupAgentPermission({ userGroupId: userGroupId }).then(response => {
        this.userGroupForm.agentIds = response.data || [];
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 提交机构权限表单 */
    submitOrgForm() {
      if (!this.orgForm.orgId) {
        this.$modal.msgError("请选择机构");
        return;
      }

      this.loading = true;

      // 构造AgentPermission对象数组
      const permissions = this.orgForm.agentIds.map(agentId => ({
        agentId: agentId,
        permissionType: 'org',
        targetId: this.orgForm.orgId,
        targetName: this.orgForm.orgName,
        permissionLevel: 'read',
        status: '0'
      }));

      setOrgAgentPermission(permissions).then(response => {
        this.$modal.msgSuccess("保存成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 提交用户组权限表单 */
    submitUserGroupForm() {
      if (!this.userGroupForm.userGroupId) {
        this.$modal.msgError("请选择用户组");
        return;
      }

      this.loading = true;

      // 构造AgentPermission对象数组
      const permissions = this.userGroupForm.agentIds.map(agentId => ({
        agentId: agentId,
        permissionType: 'user_group',
        targetId: this.userGroupForm.userGroupId,
        targetName: this.userGroupForm.userGroupName,
        permissionLevel: 'read',
        status: '0'
      }));

      setUserGroupAgentPermission(permissions).then(response => {
        this.$modal.msgSuccess("保存成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>
