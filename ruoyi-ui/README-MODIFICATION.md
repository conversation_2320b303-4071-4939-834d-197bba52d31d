# 云垚人工智能助手 - 前端修改说明

## 概述
基于RuoYi-Vue框架修改，适配云垚人工智能助手的业务需求。

## 主要功能模块

### 1. 知识库管理
- **知识库列表** (`/src/views/knowledge/knowledge/index.vue`)
  - 创建、修改、删除知识库
  - 支持公共知识库和私有知识库
  - 显示知识库文档数量

- **知识库文档管理** (`/src/views/knowledge/documents/index.vue`)
  - 上传文档到知识库
  - 解析和删除文档
  - 批量操作支持
  - 支持多种文档格式

- **知识库权限管理** (`/src/views/knowledge/permission/index.vue`)
  - 机构知识库权限设置
  - 用户组知识库权限设置

- **知识库日志管理** (`/src/views/knowledge/logs/index.vue`)
  - 文档调用日志
  - 文档下载日志

### 2. 智能体管理
- **智能体配置** (`/src/views/agent/config/index.vue`)
  - 创建和管理智能体
  - 配置API Key、模型参数等

- **智能体权限管理** (`/src/views/agent/permission/index.vue`)
  - 机构智能体权限设置
  - 用户组智能体权限设置

### 3. 机构用户组管理
- **用户组管理** (`/src/views/system/userGroup/index.vue`)
  - 创建和管理用户组
  - 用户组成员管理

### 4. 用户与权限
- **机构管理** (修改现有 `/src/views/system/dept/index.vue`)
  - 支持机构类型：公司/部门
  - 多级机构结构
  
- **用户管理** (沿用现有系统)
  - 支持三种角色：
    - 知识库超级管理员
    - 公共知识库管理员
    - 私有知识库管理员

- **机构导入导出** (`/src/views/system/importExport/index.vue`)
  - 批量导入机构和用户数据
  - 模板下载功能
  - 导入历史记录

### 5. 系统配置
- **在线用户** (沿用现有系统)
- **系统日志** (沿用现有系统)

## API接口文件

### 新增API文件
- `/src/api/knowledge/knowledge.js` - 知识库管理相关接口
- `/src/api/agent/agent.js` - 智能体管理相关接口
- `/src/api/system/userGroup.js` - 用户组管理相关接口
- `/src/api/system/importExport.js` - 导入导出相关接口

### 修改现有API
- `/src/api/system/dept.js` - 支持机构类型字段
- `/src/api/system/user.js` - 支持新的角色类型
- `/src/api/system/role.js` - 支持新的角色定义

## 页面修改

### 主要页面修改
1. **首页** (`/src/views/index.vue`)
   - 修改标题为"云垚人工智能助手"
   - 更新介绍内容和链接

2. **登录页面** (`/src/views/login.vue`)
   - 通过环境变量修改标题

3. **机构管理** (`/src/views/system/dept/index.vue`)
   - 添加机构类型字段
   - 修改相关文本标签

### 环境变量修改
- `.env.development` - 开发环境标题
- `.env.production` - 生产环境标题
- `.env.staging` - 测试环境标题

## 路由配置

### 新增路由
- `/knowledge/documents/:id` - 知识库文档管理页面

### 权限控制
- 根据用户角色显示不同的功能模块
- 实现数据权限过滤

## 部署说明

### 前端部署
1. 安装依赖：`npm install`
2. 开发环境：`npm run dev`
3. 生产构建：`npm run build:prod`

### 后端接口
详见 `api.md` 文件，包含所有需要的后端接口定义。

## 技术栈
- Vue 2.x
- Element UI
- Axios
- Vue Router
- Vuex

## 注意事项
1. 所有新增的API接口都需要后端实现
2. 权限控制需要在后端实现数据过滤
3. 文件上传功能需要配置正确的服务器路径
4. 建议在开发环境中先测试所有功能

## 联系方式
如有问题，请联系开发团队。
