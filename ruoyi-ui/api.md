# 云垚人工智能助手 - 后端API接口文档

## 1. 知识库管理模块

### 1.1 知识库列表管理
- `GET /knowledge/list` - 获取知识库列表
- `POST /knowledge` - 创建知识库
- `PUT /knowledge` - 修改知识库
- `DELETE /knowledge/{id}` - 删除知识库

### 1.2 知识库文档管理
- `GET /knowledge/{id}/documents` - 获取知识库文档列表
- `POST /knowledge/{id}/documents/upload` - 上传文档到知识库
- `PUT /knowledge/{id}/documents/{docId}/parse` - 解析文档
- `DELETE /knowledge/{id}/documents/{docId}` - 删除文档
- `POST /knowledge/{id}/documents/batch-parse` - 批量解析文档
- `DELETE /knowledge/{id}/documents/batch-delete` - 批量删除文档

### 1.3 知识库权限管理
- `GET /knowledge/permission/org` - 获取机构知识库权限
- `POST /knowledge/permission/org` - 设置机构知识库权限
- `GET /knowledge/permission/user-group` - 获取用户组知识库权限
- `POST /knowledge/permission/user-group` - 设置用户组知识库权限

### 1.4 知识库日志管理
- `GET /knowledge/logs/document-call` - 获取文档调用日志
- `GET /knowledge/logs/document-download` - 获取文档下载日志

## 2. 智能体管理模块

### 2.1 智能体配置
- `GET /agent/list` - 获取智能体列表
- `POST /agent` - 新增智能体
- `PUT /agent` - 修改智能体
- `DELETE /agent/{id}` - 删除智能体

### 2.2 智能体权限管理
- `GET /agent/permission/org` - 获取机构智能体权限
- `POST /agent/permission/org` - 设置机构智能体权限
- `GET /agent/permission/user-group` - 获取用户组智能体权限
- `POST /agent/permission/user-group` - 设置用户组智能体权限

## 3. 机构用户组管理模块

### 3.1 用户组管理
- `GET /user-group/list` - 获取用户组列表
- `POST /user-group` - 新增用户组
- `PUT /user-group` - 修改用户组
- `DELETE /user-group/{id}` - 删除用户组

## 4. 修改现有接口

### 4.1 机构管理 (复用现有dept接口)
- 现有接口：`/system/dept/*`
- 需要支持机构类型（公司/部门）的区分
- 需要支持多级机构结构

### 4.2 用户管理 (复用现有user接口)
- 现有接口：`/system/user/*`
- 需要支持角色类型：
  - 知识库超级管理员
  - 公共知识库管理员
  - 私有知识库管理员

### 4.3 角色管理 (复用现有role接口)
- 现有接口：`/system/role/*`
- 需要预设三种角色：
  - 知识库超级管理员
  - 公共知识库管理员
  - 私有知识库管理员

### 4.4 机构导入导出
- 现有接口：`/system/importExport/*`
- 需要支持批量导入机构和用户数据

### 4.5 在线用户管理
- 现有接口：`/monitor/online/*`
- 需要显示后台管理系统和前端AI应用平台的登录用户

### 4.6 系统日志
- 现有接口：`/monitor/operlog/*`
- 需要记录：
  - 用户登录/退出日志
  - 权限授权操作日志
  - 知识库操作日志

## 5. 新增接口说明

### 5.1 知识库相关接口
所有知识库相关接口都是新增的，需要后端开发团队完全实现。

### 5.2 智能体相关接口
所有智能体相关接口都是新增的，需要后端开发团队完全实现。

### 5.3 用户组相关接口
用户组管理是新增功能，需要后端开发团队实现。

## 6. 接口参数说明

### 6.1 知识库创建参数
```json
{
  "name": "知识库名称",
  "type": "public|private", // 公共库或私有库
  "description": "知识库描述",
  "createUserId": "创建用户ID",
  "orgId": "所属机构ID"
}
```

### 6.2 智能体配置参数
```json
{
  "name": "智能体名称",
  "apiKey": "API密钥",
  "model": "模型名称",
  "temperature": 0.7,
  "maxTokens": 1000,
  "description": "智能体描述"
}
```

### 6.3 用户组参数
```json
{
  "name": "用户组名称",
  "orgId": "所属机构ID",
  "userIds": ["用户ID数组"]
}
```

## 7. 权限控制说明

### 7.1 知识库权限层级
- 知识库超级管理员：可查看所有知识库
- 公共知识库管理员：只能查看自己创建的公共知识库
- 私有知识库管理员：只能查看自己部门的私有知识库

### 7.2 数据权限过滤
后端需要根据用户角色和所属机构过滤返回的数据。

## 8. 数据格式约定

### 8.1 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "total": 0
}
```

### 8.2 分页格式
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "total": 100,
  "rows": []
}
```
